import { prisma } from '../prisma'
import { getGmailClient } from './client'
import { createEmailMessage } from './content-parser'
import { 
  EmailMessage, 
  EmailOperationResult, 
  BulkEmailResult, 
  ReplyEmailData,
  EmailQuota 
} from './types'
import {
  formatErrorMessage,
  generateTrackingId,
  delay,
  shouldResetDailyCount,
  getNextResetTime,
  executeDatabaseOperation
} from './utils'

export async function sendEmail(userId: string, message: EmailMessage): Promise<EmailOperationResult> {
  try {
    const { gmail, userEmail } = await getGmailClient(userId)

    // Create email message
    const emailContent = createEmailMessage({
      from: userEmail!,
      to: message.to,
      subject: message.subject,
      htmlBody: message.htmlBody,
      textBody: message.textBody,
      trackingId: message.trackingId,
      attachments: message.attachments
    })

    // Send email
    const response = await gmail.users.messages.send({
      userId: 'me',
      requestBody: {
        raw: emailContent
      }
    })

    return {
      success: true,
      messageId: response.data.id || undefined
    }
  } catch (error) {
    console.error('Error sending email:', error)
    return {
      success: false,
      error: formatErrorMessage(error)
    }
  }
}

export async function sendBulkEmails(
  userId: string,
  campaignId: string,
  messages: Array<EmailMessage & {contactId: string}>,
  onProgress?: (sent: number, total: number, failed: number) => void
): Promise<BulkEmailResult> {
  const results: Array<{email: string, success: boolean, messageId?: string, error?: string, sentEmailId?: string}> = []
  let totalSent = 0
  let totalFailed = 0

  // Check and update daily send limit using utility
  const quota = await getEmailQuota(userId)
  
  if (quota.remaining <= 0) {
    throw new Error('Daily send limit exceeded')
  }

  // Limit messages to remaining quota
  const messagesToSend = messages.slice(0, quota.remaining)
  
  // Send emails with rate limiting (1 email per second to respect Gmail limits)
  for (let i = 0; i < messagesToSend.length; i++) {
    const message = messagesToSend[i]
    
    // Generate unique tracking ID using utility
    const trackingId = generateTrackingId()
    
    try {
      // Create initial email tracking record with consistent error handling
      const sentEmailRecord = await executeDatabaseOperation(
        () => prisma.sentEmail.create({
          data: {
            campaignId,
            contactId: message.contactId,
            trackingId,
            status: 'PENDING',
            sentAt: new Date()
          }
        }),
        'create email tracking record'
      )

      // Send the email
      const result = await sendEmail(userId, {
        ...message,
        trackingId
      })

      if (result.success) {
        totalSent++
        
        // Update tracking record with success
        if (sentEmailRecord) {
          await executeDatabaseOperation(
            () => prisma.sentEmail.update({
              where: { id: sentEmailRecord.id },
              data: {
                status: 'SENT',
                gmailMessageId: result.messageId
              }
            }),
            'update email tracking record with success'
          )
        }

        // Update user's daily send count
        await executeDatabaseOperation(
          () => prisma.user.update({
            where: { id: userId },
            data: {
              dailySendCount: { increment: 1 }
            }
          }),
          'update user daily send count'
        )

        results.push({
          email: message.to,
          success: true,
          messageId: result.messageId,
          sentEmailId: sentEmailRecord?.id
        })
      } else {
        totalFailed++
        
        // Update tracking record with failure
        if (sentEmailRecord) {
          await executeDatabaseOperation(
            () => prisma.sentEmail.update({
              where: { id: sentEmailRecord.id },
              data: {
                status: 'FAILED',
                errorMessage: result.error
              }
            }),
            'update email tracking record with failure'
          )
        }

        results.push({
          email: message.to,
          success: false,
          error: result.error,
          sentEmailId: sentEmailRecord?.id
        })
      }
    } catch (error) {
      totalFailed++
      const errorMessage = formatErrorMessage(error)
      
      results.push({
        email: message.to,
        success: false,
        error: errorMessage
      })
    }

    // Call progress callback if provided
    if (onProgress) {
      onProgress(totalSent, messagesToSend.length, totalFailed)
    }

    // Rate limiting: wait 1 second between emails using utility
    if (i < messagesToSend.length - 1) {
      await delay(1000)
    }
  }

  return {
    totalSent,
    totalFailed,
    results
  }
}

export async function replyToEmail(userId: string, replyData: ReplyEmailData): Promise<EmailOperationResult> {
  try {
    const { gmail, userEmail } = await getGmailClient(userId)

    // Get the original message to extract threading headers
    let originalMessageId = ''
    let originalReferences = ''

    if (replyData.replyToMessageId) {
      try {
        const originalMessage = await gmail.users.messages.get({
          userId: 'me',
          id: replyData.replyToMessageId,
          format: 'full'
        })

        // Extract Message-ID and References from original message
        const headers = originalMessage.data.payload?.headers || []
        const messageIdHeader = headers.find(h => h.name?.toLowerCase() === 'message-id')
        const referencesHeader = headers.find(h => h.name?.toLowerCase() === 'references')

        originalMessageId = messageIdHeader?.value || ''
        originalReferences = referencesHeader?.value || ''
      } catch (error) {
        console.error('Error fetching original message headers:', error)
      }
    }

    // Construct References header for thread continuity
    let references = originalReferences
    if (originalMessageId) {
      references = references ? `${references} ${originalMessageId}` : originalMessageId
    }

    // Generate a unique Message-ID for this reply
    const replyMessageId = `<${Date.now()}.${Math.random().toString(36).substr(2, 9)}@${userEmail?.split('@')[1] || 'gmail.com'}>`

    // Create reply message with proper threading headers
    const emailContent = createEmailMessage({
      from: userEmail!,
      to: replyData.to,
      subject: replyData.subject.startsWith('Re:') ? replyData.subject : `Re: ${replyData.subject}`,
      htmlBody: replyData.htmlBody,
      textBody: replyData.textBody,
      attachments: replyData.attachments,
      messageId: replyMessageId,
      references: references || undefined,
      inReplyTo: originalMessageId || undefined
    })

    // Send reply
    const response = await gmail.users.messages.send({
      userId: 'me',
      requestBody: {
        raw: emailContent,
        threadId: replyData.threadId
      }
    })

    return {
      success: true,
      messageId: response.data.id || undefined
    }
  } catch (error) {
    console.error('Error sending reply:', error)
    return {
      success: false,
      error: formatErrorMessage(error)
    }
  }
}

export async function getEmailQuota(userId: string): Promise<EmailQuota> {
  const user = await prisma.user.findUnique({
    where: { id: userId },
    select: {
      dailySendLimit: true,
      dailySendCount: true,
      lastSendReset: true
    }
  })

  if (!user) {
    throw new Error('User not found')
  }

  let dailyUsed = user.dailySendCount
  
  // Reset daily count if it's a new day using utility
  if (shouldResetDailyCount(user.lastSendReset)) {
    dailyUsed = 0
    await executeDatabaseOperation(
      () => prisma.user.update({
        where: { id: userId },
        data: {
          dailySendCount: 0,
          lastSendReset: new Date()
        }
      }),
      'reset daily send count',
      true // throw on error for quota operations
    )
  }

  return {
    dailyLimit: user.dailySendLimit,
    dailyUsed,
    remaining: Math.max(0, user.dailySendLimit - dailyUsed),
    resetTime: getNextResetTime()
  }
} 