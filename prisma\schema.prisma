// This is your Prisma schema file,
// learn more about it in the docs: https://pris.ly/d/prisma-schema

generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider = "postgresql"
  url      = env("DATABASE_URL")
  directUrl = env("DIRECT_URL")
}

model Account {
  id                       String  @id @default(cuid())
  userId                   String
  type                     String
  provider                 String
  providerAccountId        String
  refresh_token            String? @db.Text
  access_token             String? @db.Text
  expires_at               Int?
  token_type               String?
  scope                    String?
  id_token                 String? @db.Text
  session_state            String?
  refresh_token_expires_in Int?

  user User @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@unique([provider, providerAccountId])
}

model Session {
  id           String   @id @default(cuid())
  sessionToken String   @unique
  userId       String
  expires      DateTime
  user         User     @relation(fields: [userId], references: [id], onDelete: Cascade)
}

model User {
  id            String    @id @default(cuid())
  name          String?
  email         String    @unique
  emailVerified DateTime?
  image         String?
  createdAt     DateTime  @default(now())
  updatedAt     DateTime  @updatedAt
  accounts      Account[]
  sessions      Session[]
  calendarEvents CalendarEvent[]
  emailAnalyses EmailAnalysis[]
  chatSessions  ChatSession[]
  
  // Enhanced Gmail Integration
  gmailConnected    Boolean   @default(false)
  gmailRefreshToken String?   @db.Text  // Store encrypted
  dailySendLimit    Int       @default(250)
  dailySendCount    Int       @default(0)
  lastSendReset     DateTime? // Reset daily count
  
  // Mass mailer relationships
  campaigns     Campaign[]
  contacts      Contact[]
  templates     EmailTemplate[]
  contactLists  ContactList[]
}

model VerificationToken {
  identifier String
  token      String   @unique
  expires    DateTime

  @@unique([identifier, token])
}

// Enhanced Campaign model with detailed status
model Campaign {
  id          String         @id @default(cuid())
  name        String
  subject     String
  content     String         @db.Text
  status      CampaignStatus @default(DRAFT)
  scheduledAt DateTime?
  sentAt      DateTime?
  createdAt   DateTime       @default(now())
  updatedAt   DateTime       @updatedAt
  userId      String
  
  // Enhanced tracking fields
  totalEmails Int @default(0)
  sentCount   Int @default(0)
  failedCount Int @default(0)
  
  // Relationships
  user        User                  @relation(fields: [userId], references: [id], onDelete: Cascade)
  sentEmails  SentEmail[]
  errors      CampaignError[]
  sendQueue   SendQueue[]
  contactLists CampaignContactList[] // Many campaigns can use same list
}

enum CampaignStatus {
  DRAFT
  SCHEDULED
  SENDING
  SENT
  PAUSED
  FAILED
}

model CampaignError {
  id         String   @id @default(cuid())
  campaignId String
  contactId  String
  error      String   @db.Text
  createdAt  DateTime @default(now())
  
  campaign   Campaign @relation(fields: [campaignId], references: [id], onDelete: Cascade)
}

// Enhanced Contact model
model Contact {
  id        String   @id @default(cuid())
  email     String
  firstName String?
  lastName  String?
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt
  userId    String
  
  // Enhanced contact features
  customFields Json? // Store additional contact data
  
  // Relationships
  user         User                @relation(fields: [userId], references: [id], onDelete: Cascade)
  sentEmails   SentEmail[]
  lists        ContactListMember[]
  sendQueue    SendQueue[]
  
  @@unique([email, userId])
}

// Contact Lists/Groups
model ContactList {
  id          String   @id @default(cuid())
  name        String
  description String?
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt
  userId      String
  
  // Relationships
  user        User                  @relation(fields: [userId], references: [id], onDelete: Cascade)
  contacts    ContactListMember[]
  campaigns   CampaignContactList[] // Many campaigns can use same list
}

model ContactListMember {
  id            String      @id @default(cuid())
  contactListId String
  contactId     String
  
  contactList   ContactList @relation(fields: [contactListId], references: [id], onDelete: Cascade)
  contact       Contact     @relation(fields: [contactId], references: [id], onDelete: Cascade)
  
  @@unique([contactListId, contactId])
}

// Junction table for Campaign <-> ContactList many-to-many relationship
model CampaignContactList {
  id            String      @id @default(cuid())
  campaignId    String
  contactListId String
  
  campaign      Campaign    @relation(fields: [campaignId], references: [id], onDelete: Cascade)
  contactList   ContactList @relation(fields: [contactListId], references: [id], onDelete: Cascade)
  
  @@unique([campaignId, contactListId])
}

model EmailTemplate {
  id        String   @id @default(cuid())
  name      String
  subject   String
  content   String   @db.Text
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt
  userId    String
  user      User     @relation(fields: [userId], references: [id], onDelete: Cascade)
}

// Enhanced Email Tracking
model SentEmail {
  id             String      @id @default(cuid())
  campaignId     String?     // Make optional for standalone emails
  contactId      String
  sentAt         DateTime    @default(now())
  
  // Enhanced tracking fields
  status         EmailStatus @default(SENT)
  errorMessage   String?     @db.Text // Error messages can be long
  gmailMessageId String?     // Gmail's message ID for tracking
  
  // Tracking pixels/links
  trackingId     String?     @unique
  deliveredAt    DateTime?
  openedAt       DateTime?
  clickedAt      DateTime?
  openCount      Int         @default(0)
  clickCount     Int         @default(0)
  bounced        Boolean     @default(false)
  unsubscribed   Boolean     @default(false)
  
  campaign       Campaign?   @relation(fields: [campaignId], references: [id], onDelete: Cascade)
  contact        Contact     @relation(fields: [contactId], references: [id], onDelete: Cascade)
}

enum EmailStatus {
  PENDING
  SENT
  DELIVERED
  BOUNCED
  FAILED
  UNSUBSCRIBED
}

// Rate Limiting & Queue
model SendQueue {
  id           String      @id @default(cuid())
  campaignId   String
  contactId    String
  scheduledFor DateTime
  status       QueueStatus @default(PENDING)
  attempts     Int         @default(0)
  lastError    String?     @db.Text // Error messages can be long
  createdAt    DateTime    @default(now())
  
  campaign     Campaign    @relation(fields: [campaignId], references: [id], onDelete: Cascade)
  contact      Contact     @relation(fields: [contactId], references: [id], onDelete: Cascade)
}

enum QueueStatus {
  PENDING
  PROCESSING
  SENT
  FAILED
  CANCELLED
}

model CalendarEvent {
  id                String   @id @default(cuid())
  userId            String
  title             String   @db.Text // Encrypted - use Text for large data
  description       String?  @db.Text // Encrypted - use Text for large data
  startDate         DateTime
  endDate           DateTime?
  location          String?  @db.Text // Encrypted - use Text for large data
  category          String?
  priority          String?  // low, medium, high
  status            String?  // pending, confirmed, cancelled
  sourceType        String?  // ai-extracted, manual, gmail-auto
  sourceId          String?  // Reference to source (e.g., email ID)
  isRecurring       Boolean  @default(false) // Remove nullable, set default
  recurrencePattern String?  @db.Text // Encrypted - use Text for large data
  attendees         String?  @db.Text // Encrypted JSON array - use Text for large data
  reminderMinutes   Int?     // Minutes before event to remind
  encryptionSalt    String   // For field-level encryption
  
  user              User     @relation(fields: [userId], references: [id], onDelete: Cascade)
  
  createdAt         DateTime @default(now())
  updatedAt         DateTime @updatedAt

  @@index([userId])
  @@index([startDate])
  @@index([priority])
  @@index([status])
}

model EmailAnalysis {
  id              String   @id @default(cuid())
  userId          String
  emailId         String   // Reference to Gmail email ID
  importance      String   // high, medium, low
  summary         String   @db.Text // Encrypted - use Text for large data
  category        String
  sentiment       String   // positive, neutral, negative
  priority        Int      @default(5) // 1-10 scale
  actionItems     String?  @db.Text // Encrypted JSON array - use Text for large data
  encryptionSalt  String   // For field-level encryption
  
  user            User     @relation(fields: [userId], references: [id], onDelete: Cascade)
  
  createdAt       DateTime @default(now())
  updatedAt       DateTime @updatedAt

  @@unique([userId, emailId])
  @@index([userId])
  @@index([importance])
  @@index([category])
}

model ChatSession {
  id              String   @id @default(cuid())
  userId          String
  title           String   @db.Text // Encrypted - use Text for large data
  messages        String   @db.Text // Encrypted JSON array - use Text for large data
  context         String?  @db.Text // Encrypted JSON context - use Text for large data
  isArchived      Boolean  @default(false)
  encryptionSalt  String   // For field-level encryption
  
  user            User     @relation(fields: [userId], references: [id], onDelete: Cascade)
  
  createdAt       DateTime @default(now())
  updatedAt       DateTime @updatedAt

  @@index([userId])
} 