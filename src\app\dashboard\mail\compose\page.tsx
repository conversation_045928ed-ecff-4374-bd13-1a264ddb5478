"use client"

import { useState, useEffect } from "react"
// import { useRouter } from "next/navigation"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Textarea } from "@/components/ui/textarea"
import { Separator } from "@/components/ui/separator"
import { Badge } from "@/components/ui/badge"
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog"
import {
  ArrowLeft,
  Send,
  Eye,
  Users,
  AlertCircle,
  Check,
  X,
  Paperclip
} from "lucide-react"
import Link from "next/link"
import { FileUploadComponent } from "@/components/ui/file-upload"

interface Contact {
  id: string
  firstName: string
  lastName: string
  email: string
}

interface EmailRecipient {
  email: string
  name?: string
  type: 'to' | 'cc' | 'bcc'
}

interface FileUpload {
  file: File
  id: string
  preview?: string
}

export default function ComposePage() {
  // const router = useRouter()
  const [loading, setLoading] = useState(false)
  const [contacts, setContacts] = useState<Contact[]>([])
  const [showContactPicker, setShowContactPicker] = useState(false)
  const [previewMode, setPreviewMode] = useState(false)
  const [sendResult, setSendResult] = useState<{success: boolean, message: string} | null>(null)
  const [attachments, setAttachments] = useState<FileUpload[]>([])
  
  const [email, setEmail] = useState({
    to: [] as EmailRecipient[],
    cc: [] as EmailRecipient[],
    bcc: [] as EmailRecipient[],
    subject: "",
    content: "",
    isHtml: true
  })

  const [inputValues, setInputValues] = useState({
    toInput: "",
    ccInput: "",
    bccInput: ""
  })

  const [showCcBcc, setShowCcBcc] = useState(false)

  useEffect(() => {
    fetchContacts()
  }, [])

  const fetchContacts = async () => {
    try {
      const response = await fetch('/api/contacts')
      if (response.ok) {
        const data = await response.json()
        setContacts(data)
      }
    } catch (error) {
      console.error('Error fetching contacts:', error)
    }
  }

  const parseEmailInput = (input: string): EmailRecipient[] => {
    if (!input.trim()) return []
    
    return input.split(',').map(email => {
      const trimmed = email.trim()
      const emailMatch = trimmed.match(/([^<]+)<([^>]+)>/) || [null, null, trimmed]
      return {
        email: emailMatch[2] || trimmed,
        name: emailMatch[1]?.trim(),
        type: 'to' as const
      }
    }).filter(recipient => recipient.email)
  }

  const addRecipient = (type: 'to' | 'cc' | 'bcc', input: string) => {
    const recipients = parseEmailInput(input)
    setEmail(prev => ({
      ...prev,
      [type]: [...prev[type], ...recipients]
    }))
    setInputValues(prev => ({ ...prev, [`${type}Input`]: "" }))
  }

  const removeRecipient = (type: 'to' | 'cc' | 'bcc', index: number) => {
    setEmail(prev => ({
      ...prev,
      [type]: prev[type].filter((_, i) => i !== index)
    }))
  }

  const addContactAsRecipient = (contact: Contact, type: 'to' | 'cc' | 'bcc' = 'to') => {
    const recipient: EmailRecipient = {
      email: contact.email,
      name: `${contact.firstName} ${contact.lastName}`.trim(),
      type
    }
    
    setEmail(prev => ({
      ...prev,
      [type]: [...prev[type], recipient]
    }))
  }

  const convertFileToBase64 = (file: File): Promise<string> => {
    return new Promise((resolve, reject) => {
      const reader = new FileReader()
      reader.onload = () => {
        const result = reader.result as string
        // Remove the data URL prefix to get just the base64 content
        const base64 = result.split(',')[1]
        resolve(base64)
      }
      reader.onerror = reject
      reader.readAsDataURL(file)
    })
  }

  const handleSend = async () => {
    if (email.to.length === 0) {
      alert('Please add at least one recipient')
      return
    }

    if (!email.subject.trim()) {
      alert('Please add a subject')
      return
    }

    if (!email.content.trim()) {
      alert('Please add email content')
      return
    }

    setLoading(true)
    setSendResult(null)
    
    try {
      // Convert attachments to base64
      const emailAttachments = await Promise.all(
        attachments.map(async (attachment) => ({
          filename: attachment.file.name,
          content: await convertFileToBase64(attachment.file),
          mimeType: attachment.file.type
        }))
      )

      const allRecipients = [...email.to, ...email.cc, ...email.bcc]
      
      for (const recipient of allRecipients) {
        const response = await fetch('/api/gmail/send', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json'
          },
          body: JSON.stringify({
            to: recipient.email,
            subject: email.subject,
            htmlBody: email.isHtml ? email.content : `<pre>${email.content}</pre>`,
            textBody: email.isHtml ? email.content.replace(/<[^>]*>/g, '') : email.content,
            attachments: emailAttachments
          })
        })

        if (!response.ok) {
          const error = await response.json()
          throw new Error(`Failed to send to ${recipient.email}: ${error.message}`)
        }
      }

      setSendResult({
        success: true,
        message: `Email sent successfully to ${allRecipients.length} recipient(s)`
      })

      // Clear form after successful send
      setTimeout(() => {
        setEmail({
          to: [],
          cc: [],
          bcc: [],
          subject: "",
          content: "",
          isHtml: true
        })
        setAttachments([])
        setSendResult(null)
      }, 3000)

    } catch (error) {
      console.error('Error sending email:', error)
      setSendResult({
        success: false,
        message: error instanceof Error ? error.message : 'Failed to send email'
      })
    } finally {
      setLoading(false)
    }
  }

  const getTotalRecipients = () => {
    return email.to.length + email.cc.length + email.bcc.length
  }

  const defaultEmailTemplate = `<html>
<body style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto; padding: 20px;">
  <p>Hello,</p>
  
  <p>Your message content goes here...</p>
  
  <p>Best regards,<br/>
  Your Name</p>
</body>
</html>`

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center space-x-4">
        <Link href="/dashboard">
          <Button variant="ghost" size="icon">
            <ArrowLeft className="h-4 w-4" />
          </Button>
        </Link>
        <div>
          <h1 className="text-3xl font-bold tracking-tight">Compose Email</h1>
          <p className="text-muted-foreground">
            Send individual emails using your connected Gmail account
          </p>
        </div>
      </div>

      {/* Send Result Alert */}
      {sendResult && (
        <div className={`p-4 rounded-lg border ${
          sendResult.success 
            ? 'bg-green-50 border-green-200 text-green-800' 
            : 'bg-red-50 border-red-200 text-red-800'
        }`}>
          <div className="flex items-center space-x-2">
            {sendResult.success ? (
              <Check className="h-5 w-5" />
            ) : (
              <AlertCircle className="h-5 w-5" />
            )}
            <span className="font-medium">{sendResult.message}</span>
          </div>
        </div>
      )}

      <div className="grid gap-6 lg:grid-cols-4">
        {/* Main Compose Area */}
        <div className="lg:col-span-3 space-y-6">
          <Card>
            <CardHeader>
              <CardTitle>New Message</CardTitle>
              <CardDescription>
                Compose and send an email to individual recipients
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              {/* To Field */}
              <div className="space-y-2">
                <div className="flex items-center justify-between">
                  <Label htmlFor="to">To</Label>
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={() => setShowContactPicker(true)}
                  >
                    <Users className="mr-2 h-4 w-4" />
                    Choose from contacts
                  </Button>
                </div>
                <div className="space-y-2">
                  <Input
                    id="to"
                    value={inputValues.toInput}
                    onChange={(e) => setInputValues(prev => ({ ...prev, toInput: e.target.value }))}
                    onKeyDown={(e) => {
                      if (e.key === 'Enter' || e.key === ',') {
                        e.preventDefault()
                        addRecipient('to', inputValues.toInput)
                      }
                    }}
                    placeholder="Enter email addresses (comma separated)"
                  />
                  {email.to.length > 0 && (
                    <div className="flex flex-wrap gap-2">
                      {email.to.map((recipient, index) => (
                        <Badge key={index} variant="secondary" className="pr-1">
                          {recipient.name || recipient.email}
                          <Button
                            variant="ghost"
                            size="sm"
                            className="h-4 w-4 p-0 ml-1"
                            onClick={() => removeRecipient('to', index)}
                          >
                            <X className="h-3 w-3" />
                          </Button>
                        </Badge>
                      ))}
                    </div>
                  )}
                </div>
              </div>

              {/* CC/BCC Toggle */}
              {!showCcBcc && (
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={() => setShowCcBcc(true)}
                >
                  Add Cc/Bcc
                </Button>
              )}

              {/* CC Field */}
              {showCcBcc && (
                <div className="space-y-2">
                  <Label htmlFor="cc">Cc</Label>
                  <div className="space-y-2">
                    <Input
                      id="cc"
                      value={inputValues.ccInput}
                      onChange={(e) => setInputValues(prev => ({ ...prev, ccInput: e.target.value }))}
                      onKeyDown={(e) => {
                        if (e.key === 'Enter' || e.key === ',') {
                          e.preventDefault()
                          addRecipient('cc', inputValues.ccInput)
                        }
                      }}
                      placeholder="Enter email addresses (comma separated)"
                    />
                    {email.cc.length > 0 && (
                      <div className="flex flex-wrap gap-2">
                        {email.cc.map((recipient, index) => (
                          <Badge key={index} variant="outline" className="pr-1">
                            {recipient.name || recipient.email}
                            <Button
                              variant="ghost"
                              size="sm"
                              className="h-4 w-4 p-0 ml-1"
                              onClick={() => removeRecipient('cc', index)}
                            >
                              <X className="h-3 w-3" />
                            </Button>
                          </Badge>
                        ))}
                      </div>
                    )}
                  </div>
                </div>
              )}

              {/* BCC Field */}
              {showCcBcc && (
                <div className="space-y-2">
                  <Label htmlFor="bcc">Bcc</Label>
                  <div className="space-y-2">
                    <Input
                      id="bcc"
                      value={inputValues.bccInput}
                      onChange={(e) => setInputValues(prev => ({ ...prev, bccInput: e.target.value }))}
                      onKeyDown={(e) => {
                        if (e.key === 'Enter' || e.key === ',') {
                          e.preventDefault()
                          addRecipient('bcc', inputValues.bccInput)
                        }
                      }}
                      placeholder="Enter email addresses (comma separated)"
                    />
                    {email.bcc.length > 0 && (
                      <div className="flex flex-wrap gap-2">
                        {email.bcc.map((recipient, index) => (
                          <Badge key={index} variant="outline" className="pr-1">
                            {recipient.name || recipient.email}
                            <Button
                              variant="ghost"
                              size="sm"
                              className="h-4 w-4 p-0 ml-1"
                              onClick={() => removeRecipient('bcc', index)}
                            >
                              <X className="h-3 w-3" />
                            </Button>
                          </Badge>
                        ))}
                      </div>
                    )}
                  </div>
                </div>
              )}

              {/* Subject */}
              <div className="space-y-2">
                <Label htmlFor="subject">Subject</Label>
                <Input
                  id="subject"
                  value={email.subject}
                  onChange={(e) => setEmail(prev => ({ ...prev, subject: e.target.value }))}
                  placeholder="Enter email subject"
                />
              </div>

              {/* Content */}
              <div className="space-y-2">
                <div className="flex items-center justify-between">
                  <Label htmlFor="content">Message</Label>
                  <div className="flex items-center space-x-2">
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => setEmail(prev => ({ ...prev, isHtml: !prev.isHtml }))}
                    >
                      {email.isHtml ? "Switch to Plain Text" : "Switch to HTML"}
                    </Button>
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => setPreviewMode(!previewMode)}
                    >
                      <Eye className="mr-2 h-4 w-4" />
                      {previewMode ? "Edit" : "Preview"}
                    </Button>
                  </div>
                </div>
                
                {!previewMode ? (
                  <div className="space-y-4">
                    <Textarea
                      id="content"
                      value={email.content}
                      onChange={(e) => setEmail(prev => ({ ...prev, content: e.target.value }))}
                      placeholder={email.isHtml ? "Enter HTML content..." : "Enter your message..."}
                      className={`min-h-[300px] ${email.isHtml ? 'font-mono text-sm' : ''}`}
                    />
                    {email.isHtml && !email.content && (
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => setEmail(prev => ({ ...prev, content: defaultEmailTemplate }))}
                      >
                        Load HTML Template
                      </Button>
                    )}
                  </div>
                ) : (
                  <div className="border rounded-lg p-4 bg-white min-h-[300px]">
                    <div className="text-sm text-muted-foreground mb-2">Preview:</div>
                    {email.isHtml ? (
                      <div 
                        className="prose max-w-none"
                        dangerouslySetInnerHTML={{ 
                          __html: email.content || "<p>No content to preview</p>" 
                        }}
                      />
                    ) : (
                      <pre className="whitespace-pre-wrap font-sans">
                        {email.content || "No content to preview"}
                      </pre>
                    )}
                  </div>
                )}
              </div>

              {/* Attachments */}
              <div className="space-y-2">
                <Label className="flex items-center">
                  <Paperclip className="h-4 w-4 mr-2" />
                  Attachments
                </Label>
                <FileUploadComponent
                  onFilesChange={setAttachments}
                  maxFiles={5}
                  maxFileSize={25}
                />
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Sidebar */}
        <div className="space-y-6">
          {/* Send Options */}
          <Card>
            <CardHeader>
              <CardTitle>Send Options</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="space-y-2">
                <div className="flex justify-between text-sm">
                  <span className="text-muted-foreground">Recipients:</span>
                  <span className="font-medium">{getTotalRecipients()}</span>
                </div>
                <div className="flex justify-between text-sm">
                  <span className="text-muted-foreground">To:</span>
                  <span className="font-medium">{email.to.length}</span>
                </div>
                {email.cc.length > 0 && (
                  <div className="flex justify-between text-sm">
                    <span className="text-muted-foreground">Cc:</span>
                    <span className="font-medium">{email.cc.length}</span>
                  </div>
                )}
                {email.bcc.length > 0 && (
                  <div className="flex justify-between text-sm">
                    <span className="text-muted-foreground">Bcc:</span>
                    <span className="font-medium">{email.bcc.length}</span>
                  </div>
                )}
                {attachments.length > 0 && (
                  <div className="flex justify-between text-sm">
                    <span className="text-muted-foreground">Attachments:</span>
                    <span className="font-medium">{attachments.length}</span>
                  </div>
                )}
              </div>
              
              <Separator />
              
              <div className="space-y-2">
                <Button
                  onClick={handleSend}
                  disabled={loading || email.to.length === 0 || !email.subject.trim() || !email.content.trim()}
                  className="w-full"
                >
                  {loading ? (
                    "Sending..."
                  ) : (
                    <>
                      <Send className="mr-2 h-4 w-4" />
                      Send Email
                    </>
                  )}
                </Button>
              </div>
            </CardContent>
          </Card>

          {/* Quick Add Contacts */}
          <Card>
            <CardHeader>
              <CardTitle>Recent Contacts</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-2">
                {contacts.slice(0, 5).map((contact) => (
                  <div
                    key={contact.id}
                    className="flex items-center justify-between p-2 hover:bg-gray-50 rounded-lg cursor-pointer"
                    onClick={() => addContactAsRecipient(contact)}
                  >
                    <div>
                      <div className="font-medium text-sm">
                        {contact.firstName} {contact.lastName}
                      </div>
                      <div className="text-xs text-muted-foreground">
                        {contact.email}
                      </div>
                    </div>
                    <Button variant="ghost" size="sm">
                      Add
                    </Button>
                  </div>
                ))}
                {contacts.length === 0 && (
                  <p className="text-sm text-muted-foreground text-center py-4">
                    No contacts available
                  </p>
                )}
              </div>
            </CardContent>
          </Card>
        </div>
      </div>

      {/* Contact Picker Dialog */}
      <Dialog open={showContactPicker} onOpenChange={setShowContactPicker}>
        <DialogContent className="max-w-2xl">
          <DialogHeader>
            <DialogTitle>Choose Contacts</DialogTitle>
            <DialogDescription>
              Select contacts to add as recipients
            </DialogDescription>
          </DialogHeader>
          <div className="max-h-96 overflow-y-auto">
            <div className="space-y-2">
              {contacts.map((contact) => (
                <div
                  key={contact.id}
                  className="flex items-center justify-between p-3 border rounded-lg hover:bg-gray-50"
                >
                  <div>
                    <div className="font-medium">
                      {contact.firstName} {contact.lastName}
                    </div>
                    <div className="text-sm text-muted-foreground">
                      {contact.email}
                    </div>
                  </div>
                  <div className="space-x-1">
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => addContactAsRecipient(contact, 'to')}
                    >
                      To
                    </Button>
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => addContactAsRecipient(contact, 'cc')}
                    >
                      Cc
                    </Button>
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => addContactAsRecipient(contact, 'bcc')}
                    >
                      Bcc
                    </Button>
                  </div>
                </div>
              ))}
            </div>
          </div>
          <DialogFooter>
            <Button onClick={() => setShowContactPicker(false)}>
              Done
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  )
} 