import { NextRequest, NextResponse } from "next/server"
import { getServerSession } from "next-auth/next"
import { authOptions } from "@/lib/auth"
import { prisma } from "@/lib/prisma"

export async function GET(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions) as any
    
    if (!session?.user?.email) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 })
    }

    // Get user from database
    const user = await prisma.user.findUnique({
      where: { email: session.user.email }
    })

    if (!user) {
      return NextResponse.json({ error: "User not found" }, { status: 404 })
    }

    // Get stats in parallel
    const [
      totalCampaigns,
      totalContacts,
      emailsSentToday
    ] = await Promise.all([
      // Total campaigns
      prisma.campaign.count({
        where: { userId: user.id }
      }),
      
      // Total contacts
      prisma.contact.count({
        where: { userId: user.id }
      }),
      
      // Emails sent today
      prisma.sentEmail.count({
        where: {
          campaign: {
            userId: user.id
          },
          sentAt: {
            gte: new Date(new Date().setHours(0, 0, 0, 0)) // Start of today
          }
        }
      })
    ])

    // Check Gmail connection status - user is connected if they have the flag set
    // We don't strictly need a refresh token for basic connection status
    const gmailConnected = user.gmailConnected

    const stats = {
      totalCampaigns,
      totalContacts,
      emailsSentToday,
      gmailConnected
    }

    return NextResponse.json(stats)
  } catch (error) {
    console.error("Error fetching dashboard stats:", error)
    return NextResponse.json(
      { error: "Internal server error" },
      { status: 500 }
    )
  }
} 