import { NextRequest, NextResponse } from 'next/server'
import { getServerSession } from "next-auth/next"
import { authOptions } from '@/lib/auth'
import { prisma } from '@/lib/prisma'
import { calendarEncryption, DatabaseCalendarEvent, EncryptedCalendarEvent } from '@/lib/calendar-encryption'

export async function GET(request: NextRequest) {
  try {
 
    const session = await getServerSession(authOptions) as any
    
    if (!session?.user?.email) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    // Get the user record to get the actual user ID
    const user = await prisma.user.findUnique({
      where: { email: session.user.email }
    })

    if (!user) {
      return NextResponse.json({ error: 'User not found' }, { status: 404 })
    }

    const { searchParams } = new URL(request.url)
    const startDate = searchParams.get('startDate')
    const endDate = searchParams.get('endDate')
    const category = searchParams.get('category')
    const status = searchParams.get('status')
    const sourceType = searchParams.get('sourceType')

    // Build query filters
    const where: any = {
      userId: user.id
    }

    if (startDate && endDate) {
      where.startDate = {
        gte: new Date(startDate),
        lte: new Date(endDate)
      }
    }

    if (category && category !== 'all') {
      where.category = category
    }

    if (status && status !== 'all') {
      where.status = status
    }

    if (sourceType && sourceType !== 'all') {
      where.sourceType = sourceType
    }

    // Fetch encrypted events from database
    const dbEvents = await prisma.calendarEvent.findMany({
      where,
      orderBy: {
        startDate: 'asc'
      }
    })

    // Decrypt events for client with improved error handling
    const decryptedEvents = await calendarEncryption.decryptCalendarEvents(dbEvents as any)

    return NextResponse.json({
      events: decryptedEvents.filter(event => event.title), // Filter out any invalid events
      count: decryptedEvents.length
    })

  } catch (error) {
    console.error('Calendar fetch error:', error)
    return NextResponse.json(
      { error: 'Failed to fetch calendar events' },
      { status: 500 }
    )
  }
}

export async function POST(request: NextRequest) {
  try {
 
    const session = await getServerSession(authOptions) as any
    
    if (!session?.user?.email) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    // Get the user record to get the actual user ID
    const user = await prisma.user.findUnique({
      where: { email: session.user.email }
    })

    if (!user) {
      return NextResponse.json({ error: 'User not found' }, { status: 404 })
    }

    const eventData: EncryptedCalendarEvent = await request.json()

    // Validate required fields
    if (!eventData.title || !eventData.startDate) {
      return NextResponse.json(
        { error: 'Title and start date are required' },
        { status: 400 }
      )
    }

    // Validate and normalize dates
    const startDate = new Date(eventData.startDate)
    const endDate = eventData.endDate ? new Date(eventData.endDate) : undefined

    if (isNaN(startDate.getTime())) {
      return NextResponse.json(
        { error: 'Invalid start date' },
        { status: 400 }
      )
    }

    if (endDate && isNaN(endDate.getTime())) {
      return NextResponse.json(
        { error: 'Invalid end date' },
        { status: 400 }
      )
    }

    // Add user ID and normalize dates
    const normalizedEventData = {
      ...eventData,
      userId: user.id,
      startDate,
      endDate
    }

    // Encrypt event data
    const encryptedEvent = await calendarEncryption.encryptCalendarEvent(normalizedEventData)

    // Save to database
    const dbEvent = await prisma.calendarEvent.create({
      data: {
        ...encryptedEvent,
        startDate: encryptedEvent.startDate,
        endDate: encryptedEvent.endDate
      }
    })

    // Return decrypted event
    const decryptedEvent = await calendarEncryption.decryptCalendarEvent(dbEvent as any)

    if (!decryptedEvent) {
      return NextResponse.json(
        { error: 'Failed to decrypt created event' },
        { status: 500 }
      )
    }

    return NextResponse.json({
      event: decryptedEvent,
      message: 'Event created successfully'
    })

  } catch (error) {
    console.error('Calendar creation error:', error)
    return NextResponse.json(
      { error: 'Failed to create calendar event' },
      { status: 500 }
    )
  }
}

export async function PUT(request: NextRequest) {
  try {
 
    const session = await getServerSession(authOptions) as any
    
    if (!session?.user?.email) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    // Get the user record to get the actual user ID
    const user = await prisma.user.findUnique({
      where: { email: session.user.email }
    })

    if (!user) {
      return NextResponse.json({ error: 'User not found' }, { status: 404 })
    }

    const { id, ...eventData }: EncryptedCalendarEvent & { id: string } = await request.json()

    if (!id) {
      return NextResponse.json({ error: 'Event ID is required' }, { status: 400 })
    }

    // Check if event exists and belongs to user
    const existingEvent = await prisma.calendarEvent.findFirst({
      where: {
        id,
        userId: user.id
      }
    })

    if (!existingEvent) {
      return NextResponse.json({ error: 'Event not found' }, { status: 404 })
    }

    // Validate dates if provided
    const startDate = eventData.startDate ? new Date(eventData.startDate) : existingEvent.startDate
    const endDate = eventData.endDate ? new Date(eventData.endDate) : existingEvent.endDate

    if (eventData.startDate && isNaN(startDate.getTime())) {
      return NextResponse.json(
        { error: 'Invalid start date' },
        { status: 400 }
      )
    }

    if (eventData.endDate && endDate && isNaN(endDate.getTime())) {
      return NextResponse.json(
        { error: 'Invalid end date' },
        { status: 400 }
      )
    }

    // Prepare update data
    const updateData = {
      ...eventData,
      userId: user.id,
      id,
      startDate,
      endDate
    }

    // Encrypt updated event data
    const encryptedEvent = await calendarEncryption.encryptCalendarEvent(updateData as any)

    // Update in database
    const updatedDbEvent = await prisma.calendarEvent.update({
      where: { id },
      data: {
        ...encryptedEvent,
        startDate: encryptedEvent.startDate,
        endDate: encryptedEvent.endDate
      }
    })

    // Return decrypted event
    const decryptedEvent = await calendarEncryption.decryptCalendarEvent(updatedDbEvent as any)

    if (!decryptedEvent) {
      return NextResponse.json(
        { error: 'Failed to decrypt updated event' },
        { status: 500 }
      )
    }

    return NextResponse.json({
      event: decryptedEvent,
      message: 'Event updated successfully'
    })

  } catch (error) {
    console.error('Calendar update error:', error)
    return NextResponse.json(
      { error: 'Failed to update calendar event' },
      { status: 500 }
    )
  }
}

export async function DELETE(request: NextRequest) {
  try {
 
    const session = await getServerSession(authOptions) as any
    
    if (!session?.user?.email) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    const { searchParams } = new URL(request.url)
    const id = searchParams.get('id')

    if (!id) {
      return NextResponse.json({ error: 'Event ID is required' }, { status: 400 })
    }

    // Check if event exists and belongs to user
    const existingEvent = await prisma.calendarEvent.findFirst({
      where: {
        id,
        userId: session.user.id
      }
    })

    if (!existingEvent) {
      return NextResponse.json({ error: 'Event not found' }, { status: 404 })
    }

    // Delete from database
    await prisma.calendarEvent.delete({
      where: { id }
    })

    return NextResponse.json({
      message: 'Event deleted successfully'
    })

  } catch (error) {
    console.error('Calendar deletion error:', error)
    return NextResponse.json(
      { error: 'Failed to delete calendar event' },
      { status: 500 }
    )
  }
} 