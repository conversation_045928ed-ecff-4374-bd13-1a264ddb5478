"use client"

import { useState, useEffect } from "react"
import { Calendar } from "@/components/ui/calendar"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Button } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogTrigger } from "@/components/ui/dialog"
import { Input } from "@/components/ui/input"
import { Textarea } from "@/components/ui/textarea"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Plus, Grid3X3, List, BarChart3, Clock, Calendar as CalendarIcon } from "lucide-react"
import { format, isSameDay, startOfMonth, endOfMonth, eachDayOfInterval, isToday } from "date-fns"

interface CalendarEvent {
  id: string
  title: string
  description?: string
  date: Date
  priority: "low" | "medium" | "high"
  category: string
}

type ViewMode = "month" | "timeline" | "agenda"

export function CalendarView() {
  const [selectedDate, setSelectedDate] = useState<Date>(new Date())
  const [viewMode, setViewMode] = useState<ViewMode>("month")
  const [events, setEvents] = useState<CalendarEvent[]>([
    {
      id: "1",
      title: "Team Meeting",
      description: "Weekly team sync",
      date: new Date(),
      priority: "high",
      category: "Work"
    },
    {
      id: "2",
      title: "Project Deadline",
      description: "Submit final draft",
      date: new Date(Date.now() + 24 * 60 * 60 * 1000),
      priority: "high",
      category: "Work"
    }
  ])
  const [newEvent, setNewEvent] = useState<{
    title: string
    description: string
    priority: "low" | "medium" | "high"
    category: string
  }>({
    title: "",
    description: "",
    priority: "medium",
    category: ""
  })
  const [showDialog, setShowDialog] = useState(false)

  const currentMonth = startOfMonth(selectedDate)
  const monthEnd = endOfMonth(selectedDate)
  const daysInMonth = eachDayOfInterval({ start: currentMonth, end: monthEnd })

  const getEventsForDate = (date: Date) => {
    return events.filter(event => isSameDay(event.date, date))
  }

  const getPriorityColor = (priority: string) => {
    switch (priority) {
      case "high": return "bg-red-100 text-red-800 border-red-200 dark:bg-red-950/20 dark:text-red-400 dark:border-red-800"
      case "medium": return "bg-yellow-100 text-yellow-800 border-yellow-200 dark:bg-yellow-950/20 dark:text-yellow-400 dark:border-yellow-800"
      case "low": return "bg-green-100 text-green-800 border-green-200 dark:bg-green-950/20 dark:text-green-400 dark:border-green-800"
      default: return "bg-blue-100 text-blue-800 border-blue-200 dark:bg-blue-950/20 dark:text-blue-400 dark:border-blue-800"
    }
  }

  const addEvent = () => {
    if (newEvent.title) {
      const event: CalendarEvent = {
        id: Date.now().toString(),
        ...newEvent,
        date: selectedDate
      }
      setEvents([...events, event])
      setNewEvent({ title: "", description: "", priority: "medium", category: "" })
      setShowDialog(false)
    }
  }

  const renderMonthView = () => (
    <div className="space-y-6">
      {/* Current Date Display */}
      <div className="bg-gradient-to-r from-blue-500 to-blue-600 rounded-xl p-6 text-white shadow-lg">
        <div className="flex items-center gap-4">
          <div className="bg-white/20 rounded-lg p-3">
            <CalendarIcon className="h-8 w-8" />
          </div>
          <div>
            <div className="text-blue-100 text-sm font-medium">Today</div>
            <div className="text-2xl font-bold">{format(new Date(), "MMMM dd, yyyy")}</div>
            <div className="text-blue-100 text-sm">{format(new Date(), "EEEE")}</div>
          </div>
        </div>
      </div>

      <div className="grid gap-6 lg:grid-cols-3">
        <div className="lg:col-span-2">
          <Card className="border-slate-200 dark:border-slate-700 shadow-sm">
            <CardHeader className="pb-4">
              <CardTitle className="text-xl font-semibold text-slate-900 dark:text-slate-100">
                {format(selectedDate, "MMMM yyyy")}
              </CardTitle>
            </CardHeader>
            <CardContent className="p-0">
              <Calendar
                mode="single"
                selected={selectedDate}
                onSelect={(date: Date | undefined) => date && setSelectedDate(date)}
                className="w-full rounded-none border-0"
                modifiers={{
                  hasEvents: (date: Date) => getEventsForDate(date).length > 0,
                  today: new Date(),
                }}
                modifiersStyles={{
                  hasEvents: {
                    backgroundColor: 'rgba(59, 130, 246, 0.1)',
                    borderRadius: '8px',
                    fontWeight: '600'
                  },
                  today: {
                    backgroundColor: '#2563eb',
                    color: 'white',
                    fontWeight: 'bold',
                    boxShadow: '0 0 0 2px #3b82f6, 0 0 0 4px rgba(59, 130, 246, 0.3)'
                  }
                }}
              />
            </CardContent>
          </Card>
        </div>

        <div className="space-y-4">
          <Card className="border-slate-200 dark:border-slate-700 shadow-sm">
            <CardHeader className="pb-3">
              <div className="flex items-center justify-between">
                <CardTitle className="text-lg font-semibold text-slate-900 dark:text-slate-100">
                  {format(selectedDate, "MMMM dd")}
                </CardTitle>
                {isToday(selectedDate) && (
                  <Badge variant="secondary" className="bg-blue-100 text-blue-700 dark:bg-blue-950/30 dark:text-blue-400">
                    Today
                  </Badge>
                )}
              </div>
            </CardHeader>
            <CardContent className="space-y-3">
              {getEventsForDate(selectedDate).length === 0 ? (
                <p className="text-slate-500 dark:text-slate-400 text-sm py-4 text-center">
                  No events scheduled
                </p>
              ) : (
                getEventsForDate(selectedDate).slice(0, 2).map((event) => (
                  <div
                    key={event.id}
                    className="p-3 rounded-lg border border-slate-200 dark:border-slate-700 bg-slate-50/50 dark:bg-slate-900/20 hover:bg-slate-100 dark:hover:bg-slate-800/30 transition-colors"
                  >
                    <div className="flex items-start justify-between gap-2">
                      <div className="flex-1 min-w-0">
                        <h4 className="font-medium text-sm text-slate-900 dark:text-slate-100 truncate">
                          {event.title}
                        </h4>
                        {event.description && (
                          <p className="text-xs text-slate-600 dark:text-slate-400 mt-1 line-clamp-2">
                            {event.description}
                          </p>
                        )}
                      </div>
                      <Badge variant="outline" className={`text-xs shrink-0 ${getPriorityColor(event.priority)}`}>
                        {event.priority}
                      </Badge>
                    </div>
                  </div>
                ))
              )}
              
              {getEventsForDate(selectedDate).length > 2 && (
                <p className="text-xs text-slate-500 dark:text-slate-400 text-center py-2">
                  +{getEventsForDate(selectedDate).length - 2} more events
                </p>
              )}

              <Dialog open={showDialog} onOpenChange={setShowDialog}>
                <DialogTrigger asChild>
                  <Button variant="outline" size="sm" className="w-full mt-4 h-9">
                    <Plus className="h-4 w-4 mr-2" />
                    Add Event
                  </Button>
                </DialogTrigger>
                <DialogContent className="max-w-lg">
                  <DialogHeader>
                    <DialogTitle>Create New Event</DialogTitle>
                  </DialogHeader>
                  <div className="space-y-4 pt-4">
                    <div>
                      <label className="text-sm font-medium text-slate-700 dark:text-slate-300">Event Title</label>
                      <Input
                        value={newEvent.title}
                        onChange={(e) => setNewEvent({ ...newEvent, title: e.target.value })}
                        placeholder="Enter event title..."
                        className="mt-1"
                      />
                    </div>
                    <div>
                      <label className="text-sm font-medium text-slate-700 dark:text-slate-300">Description</label>
                      <Textarea
                        value={newEvent.description}
                        onChange={(e) => setNewEvent({ ...newEvent, description: e.target.value })}
                        placeholder="Add description..."
                        className="mt-1 resize-none"
                        rows={3}
                      />
                    </div>
                    <div className="grid grid-cols-2 gap-4">
                      <div>
                        <label className="text-sm font-medium text-slate-700 dark:text-slate-300">Priority</label>
                        <Select
                          value={newEvent.priority}
                          onValueChange={(value) =>
                            setNewEvent({ ...newEvent, priority: value as "low" | "medium" | "high" })
                          }
                        >
                          <SelectTrigger className="mt-1">
                            <SelectValue />
                          </SelectTrigger>
                          <SelectContent>
                            <SelectItem value="low">Low</SelectItem>
                            <SelectItem value="medium">Medium</SelectItem>
                            <SelectItem value="high">High</SelectItem>
                          </SelectContent>
                        </Select>
                      </div>
                      <div>
                        <label className="text-sm font-medium text-slate-700 dark:text-slate-300">Category</label>
                        <Input
                          value={newEvent.category}
                          onChange={(e) => setNewEvent({ ...newEvent, category: e.target.value })}
                          placeholder="Work, Personal..."
                          className="mt-1"
                        />
                      </div>
                    </div>
                    <div className="flex gap-3 pt-4">
                      <Button onClick={addEvent} className="flex-1">
                        Create Event
                      </Button>
                      <Button variant="outline" onClick={() => setShowDialog(false)}>
                        Cancel
                      </Button>
                    </div>
                  </div>
                </DialogContent>
              </Dialog>
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  )

  const renderTimelineView = () => (
    <div className="space-y-4">
      {events
        .sort((a, b) => a.date.getTime() - b.date.getTime())
        .map((event) => (
          <Card key={event.id} className="border-slate-200 dark:border-slate-700 shadow-sm hover:shadow-md transition-shadow">
            <CardContent className="p-4">
              <div className="flex items-start gap-4">
                <div className="flex flex-col items-center">
                  <div className="bg-blue-100 dark:bg-blue-950/30 text-blue-700 dark:text-blue-400 rounded-lg p-2">
                    <Clock className="h-4 w-4" />
                  </div>
                  <div className="text-xs text-slate-500 dark:text-slate-400 mt-2 text-center">
                    {format(event.date, "MMM dd")}
                  </div>
                </div>
                <div className="flex-1">
                  <div className="flex items-start justify-between">
                    <div>
                      <h3 className="font-semibold text-slate-900 dark:text-slate-100">{event.title}</h3>
                      {event.description && (
                        <p className="text-sm text-slate-600 dark:text-slate-400 mt-1">{event.description}</p>
                      )}
                      <div className="flex items-center gap-2 mt-2">
                        <Badge variant="outline" className={`text-xs ${getPriorityColor(event.priority)}`}>
                          {event.priority}
                        </Badge>
                        {event.category && (
                          <Badge variant="secondary" className="text-xs">
                            {event.category}
                          </Badge>
                        )}
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        ))}
    </div>
  )

  return (
    <div className="max-w-7xl mx-auto p-6 space-y-6">
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold text-slate-900 dark:text-slate-100">Calendar</h1>
          <p className="text-slate-600 dark:text-slate-400 mt-1">Manage your schedule and events</p>
        </div>
        
        <div className="flex items-center gap-2">
          <div className="flex items-center bg-slate-100 dark:bg-slate-800 rounded-lg p-1">
            <Button
              variant={viewMode === "month" ? "secondary" : "ghost"}
              size="sm"
              onClick={() => setViewMode("month")}
              className="h-8 px-3"
            >
              <Grid3X3 className="h-4 w-4" />
            </Button>
            <Button
              variant={viewMode === "timeline" ? "secondary" : "ghost"}
              size="sm"
              onClick={() => setViewMode("timeline")}
              className="h-8 px-3"
            >
              <List className="h-4 w-4" />
            </Button>
            <Button
              variant={viewMode === "agenda" ? "secondary" : "ghost"}
              size="sm"
              onClick={() => setViewMode("agenda")}
              className="h-8 px-3"
            >
              <BarChart3 className="h-4 w-4" />
            </Button>
          </div>
        </div>
      </div>

      {viewMode === "month" && renderMonthView()}
      {viewMode === "timeline" && renderTimelineView()}
      {viewMode === "agenda" && renderTimelineView()}
    </div>
  )
} 