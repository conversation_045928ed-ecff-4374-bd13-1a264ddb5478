// Centralized email types to eliminate duplication across components

export interface EmailAttachment {
  filename: string
  mimeType: string
  size: number
  attachmentId: string
}

export interface EmailLabel {
  id: string
  name: string
  type: 'system' | 'user'
  color?: string
}

export interface BaseEmail {
  id: string
  threadId: string
  from: string
  subject: string
  date: Date | string
  snippet: string
  isRead: boolean
  isStarred: boolean
  hasAttachments: boolean
  labels?: EmailLabel[]
  attachments?: EmailAttachment[]
  profilePhoto?: string
}

export interface EmailDetail extends BaseEmail {
  to?: string
  cc?: string
  bcc?: string
  replyTo?: string
  body: string
  importance?: 'high' | 'normal' | 'low'
  messageId?: string
  references?: string
  inReplyTo?: string
}

// Thread-specific types
export interface ThreadMessage extends EmailDetail {
  // All properties from EmailDetail are included
}

export interface EmailThread {
  threadId: string
  messageCount: number
  messages: ThreadMessage[]
  latestMessage: ThreadMessage
  historyId?: string
  hasUnread: boolean
  participants: string[]
}

export interface ThreadGroup {
  threadId: string
  messages: BaseEmail[]
  latestMessage: BaseEmail
  messageCount: number
  hasUnread: boolean
  participants: string[]
}

// Component Props
export interface EmailRendererProps {
  body: string
  className?: string
  enableQuoteToggle?: boolean
}

export interface EmailViewerProps {
  email: EmailDetail
  onAction?: (action: string, emailId: string) => void
  showActions?: boolean
  className?: string
}

export interface EmailPageLayoutProps {
  title: string
  icon: any
  apiEndpoint: string
  emptyMessage?: string
  showDateRange?: boolean
  pageContext?: 'inbox' | 'archive' | 'trash' | 'spam' | 'sent' | 'starred' | 'categories' | 'drafts'
}

export interface EmailContentWrapperProps {
  emailId: string | null
  onEmailUpdate?: () => void
}

export interface EmailThreadViewerProps {
  threadId: string
  initialEmail?: ThreadMessage
  onAction?: (action: string, messageId: string) => void
  showActions?: boolean
  className?: string
  showAllMessages?: boolean
  expandedMessageIds?: Set<string>
  onToggleMessage?: (messageId: string) => void
}

// Gmail specific types
export interface GmailLabel {
  id: string
  name: string
  type: string
}

// Utility types
export type EmailAction = 'star' | 'archive' | 'trash' | 'reply' | 'replyAll' | 'forward' | 'markUnread' | 'moveToInbox' | 'deletePermanently' | 'notSpam'

export type ComposerType = 'reply' | 'replyAll' | 'forward' 