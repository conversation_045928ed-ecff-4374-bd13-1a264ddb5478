// Export all types
export * from './gmail/types'

// Export client functions
export { getGmailClient, testGmailConnection } from './gmail/client'

// Export email operations
export { 
  sendEmail, 
  sendBulkEmails, 
  replyToEmail, 
  getEmailQuota 
} from './gmail/operations'

// Export email retrieval functions
export { 
  getInboxEmails,
  getEmailDetails,
  getSentEmails,
  getDraftEmails,
  getArchivedEmails,
  getTrashEmails,
  getSpamEmails,
  getStarredEmails,
  getCategoryEmails
} from './gmail/retrieval'

// Export email actions
export {
  markEmailAsRead,
  markEmailAsUnread,
  deleteEmail,
  archiveEmail,
  starEmail,
  addEmailLabel,
  removeEmailLabel,
  moveToInbox,
  deletePermanently,
  restoreEmail,
  markAsNotSpam,
  getGmailLabels,
  createGmailLabel,
  bulkEmailOperations,
  bulkDeletePermanently
} from './gmail/actions'

// Export content parsing utilities
export {
  extractEnhancedEmailBody,
  checkForAttachments,
  extractAttachments,
  createEmailMessage
} from './gmail/content-parser'

// Export utility functions
export {
  buildGmailQuery,
  extractHeader,
  formatHeaders,
  formatGmailListResponse,
  generateTrackingId,
  delay,
  shouldResetDailyCount,
  getNextResetTime
} from './gmail/utils'

// Export API utilities for route handlers
export {
  authenticateGmailUser,
  parseEmailListParams,
  createSuccessResponse,
  createErrorResponse,
  withGmailAuth,
  formatEmailOperationResult,
  validateRequiredFields,
  formatBulkOperationResult
} from './gmail/api-utils'

export type { AuthenticatedUser, GmailApiContext } from './gmail/api-utils' 