"use client"

import Link from "next/link"
import { usePathname, useSearchParams } from "next/navigation"
import { useState, useEffect } from "react"
import { Badge } from "@/components/ui/badge"
import { But<PERSON> } from "@/components/ui/button"
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar"
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu"
import {
  Sidebar,
  SidebarContent,
  SidebarFooter,
  SidebarGroup,
  SidebarGroupContent,
  SidebarGroupLabel,
  SidebarHeader,
  SidebarMenu,
  SidebarMenuButton,
  SidebarMenuItem,
  SidebarSeparator
} from "@/components/ui/sidebar"
import {
  BarChart3,
  Mail,
  Users,
  FileText,
  Settings,
  Send,
  Plus,
  Home,
  Zap,
  History,
  LogOut,
  User,
  ChevronsUpDown,
  PlusCircle,
  Layout,
  Inbox,
  Bot,
  Calendar,
  UserX,
  Edit3,
  SendHorizontal,
  Archive,
  Trash2,
  Tags,
  Tag,
  Shield,
  FolderOpen
} from "lucide-react"
import { ChatHistory } from "./chat-history"

interface AppSidebarProps {
  gmailConnected?: boolean
  user?: {
    id: string
    email: string
    name?: string | null
    image?: string | null
  }
  onSignOut?: () => void
}

interface CategoryInfo {
  id: string
  name: string
  count: number
}

export function AppSidebar({ gmailConnected = false, user, onSignOut }: AppSidebarProps) {
  const pathname = usePathname()
  const searchParams = useSearchParams()
  const isAIPage = pathname === '/dashboard/ai'
  const [categories, setCategories] = useState<CategoryInfo[]>([])
  const [loadingCategories, setLoadingCategories] = useState(false)
  
  // Get current category from URL params
  const currentCategory = searchParams.get('category')

  // Fetch categories when Gmail is connected
  useEffect(() => {
    if (gmailConnected) {
      fetchCategories()
    }
  }, [gmailConnected])

  const fetchCategories = async () => {
    try {
      setLoadingCategories(true)
      // For now, we'll use predefined categories since Gmail API categories are standard
      // In a real implementation, you might want to fetch actual counts from the API
      const defaultCategories: CategoryInfo[] = [
        { id: "primary", name: "Primary", count: 0 },
        { id: "social", name: "Social", count: 0 },
        { id: "promotions", name: "Promotions", count: 0 },
        { id: "updates", name: "Updates", count: 0 },
        { id: "forums", name: "Forums", count: 0 }
      ]
      setCategories(defaultCategories)
    } catch (error) {
      console.error('Error fetching categories:', error)
    } finally {
      setLoadingCategories(false)
    }
  }

  const mainRoutes = [
    {
      label: "Dashboard",
      icon: Home,
      href: "/dashboard",
      active: pathname === "/dashboard"
    },
    {
      label: "Subscriptions",
      icon: UserX,
      href: "/dashboard/subscriptions",
      active: pathname === "/dashboard/subscriptions"
    },
    {
      label: "AI Assistant",
      icon: Bot,
      href: "/dashboard/ai",
      active: pathname === "/dashboard/ai"
    },
    {
      label: "Smart Calendar",
      icon: Calendar,
      href: "/dashboard/calendar",
      active: pathname === "/dashboard/calendar"
    }
  ]

  const mailRoutes = [
    {
      label: "Inbox",
      icon: Inbox,
      href: "/dashboard/mail/inbox",
      active: pathname === "/dashboard/mail/inbox" || pathname === "/dashboard/mail/inbox"
    },
    {
      label: "Compose",
      icon: Edit3,
      href: "/dashboard/mail/compose",
      active: pathname === "/dashboard/mail/compose" || pathname === "/dashboard/compose"
    },
    {
      label: "Sent",
      icon: SendHorizontal,
      href: "/dashboard/mail/sent",
      active: pathname === "/dashboard/mail/sent"
    },
    {
      label: "Drafts",
      icon: FileText,
      href: "/dashboard/mail/drafts",
      active: pathname === "/dashboard/mail/drafts"
    },
    {
      label: "Archive",
      icon: Archive,
      href: "/dashboard/mail/archive",
      active: pathname === "/dashboard/mail/archive"
    },
    {
      label: "Trash",
      icon: Trash2,
      href: "/dashboard/mail/trash",
      active: pathname === "/dashboard/mail/trash"
    },
    {
      label: "Labels",
      icon: Tags,
      href: "/dashboard/mail/labels",
      active: pathname === "/dashboard/mail/labels"
    },
    {
      label: "Spam",
      icon: Shield,
      href: "/dashboard/mail/spam",
      active: pathname === "/dashboard/mail/spam"
    }
  ]

  const campaignRoutes = [
    {
      label: "Campaigns",
      icon: Mail,
      href: "/dashboard/campaigns",
      active: pathname?.startsWith("/dashboard/campaigns")
    },
    {
      label: "Templates",
      icon: Layout,
      href: "/dashboard/templates",
      active: pathname?.startsWith("/dashboard/templates")
    },
    {
      label: "Email History",
      icon: History,
      href: "/dashboard/emails",
      active: pathname?.startsWith("/dashboard/emails")
    }
  ]

  const managementRoutes = [
    {
      label: "Contacts",
      icon: Users,
      href: "/dashboard/contacts",
      active: pathname?.startsWith("/dashboard/contacts")
    },
    {
      label: "Analytics",
      icon: BarChart3,
      href: "/dashboard/analytics",
      active: pathname?.startsWith("/dashboard/analytics")
    }
  ]

  const quickActions = [
    {
      label: "New Campaign",
      icon: Plus,
      href: "/dashboard/campaigns/new"
    },
    {
      label: "Manage Contacts",
      icon: PlusCircle,
      href: "/dashboard/contacts"
    }
  ]

  return (
    <Sidebar collapsible="icon" className="overflow-hidden">
      <SidebarHeader className="h-16 border-sidebar-border border-b">
        <SidebarMenu>
          <SidebarMenuItem>
            <SidebarMenuButton size="lg" asChild>
              <div>
                <div className="flex aspect-square size-8 items-center justify-center rounded-lg bg-sidebar-primary text-sidebar-primary-foreground">
                  <Send className="size-4" />
                </div>
                <div className="grid flex-1 text-left text-sm leading-tight">
                  <span className="truncate font-semibold">Eagle Mailer</span>
                  <span className="truncate text-xs">Mass Email Platform</span>
                </div>
              </div>
            </SidebarMenuButton>
          </SidebarMenuItem>
        </SidebarMenu>
      </SidebarHeader>
      
      <SidebarContent className="overflow-y-auto overflow-x-hidden">
        {/* Main Navigation - Always show */}
        <SidebarGroup>
          <SidebarGroupLabel>Main</SidebarGroupLabel>
          <SidebarGroupContent>
            <SidebarMenu>
              {mainRoutes.map((route) => (
                <SidebarMenuItem key={route.href}>
                  <SidebarMenuButton asChild isActive={route.active}>
                    <Link href={route.href}>
                      <route.icon />
                      <span>{route.label}</span>
                    </Link>
                  </SidebarMenuButton>
                </SidebarMenuItem>
              ))}
            </SidebarMenu>
          </SidebarGroupContent>
        </SidebarGroup>

        {/* Mail Section - Always show when not on AI page */}
        {!isAIPage && (
          <SidebarGroup>
            <SidebarGroupLabel>Mail</SidebarGroupLabel>
            <SidebarGroupContent>
              <SidebarMenu>
                {mailRoutes.map((route) => (
                  <SidebarMenuItem key={route.href}>
                    <SidebarMenuButton asChild isActive={route.active}>
                      <Link href={route.href}>
                        <route.icon />
                        <span>{route.label}</span>
                      </Link>
                    </SidebarMenuButton>
                  </SidebarMenuItem>
                ))}
              </SidebarMenu>
            </SidebarGroupContent>
          </SidebarGroup>
        )}

        {/* Categories Section - Show when Gmail is connected and not on AI page */}
        {!isAIPage && gmailConnected && (
          <SidebarGroup>
            <SidebarGroupLabel>Categories</SidebarGroupLabel>
            <SidebarGroupContent>
              <SidebarMenu>
                {loadingCategories ? (
                  <SidebarMenuItem>
                    <SidebarMenuButton disabled>
                      <FolderOpen className="animate-pulse" />
                      <span>Loading...</span>
                    </SidebarMenuButton>
                  </SidebarMenuItem>
                ) : (
                  categories.map((category) => (
                    <SidebarMenuItem key={category.id}>
                      <SidebarMenuButton asChild isActive={pathname === '/dashboard/mail/categories' && currentCategory === category.id}>
                        <Link href={`/dashboard/mail/categories?category=${category.id}`}>
                          <Tag />
                          <span>{category.name}</span>
                          {category.count > 0 && (
                            <Badge variant="secondary" className="ml-auto text-xs">
                              {category.count}
                            </Badge>
                          )}
                        </Link>
                      </SidebarMenuButton>
                    </SidebarMenuItem>
                  ))
                )}
                {/* All Categories Link */}
                <SidebarMenuItem>
                  <SidebarMenuButton asChild isActive={pathname === "/dashboard/mail/categories" && (!currentCategory || currentCategory === 'all')}>
                    <Link href="/dashboard/mail/categories">
                      <FolderOpen />
                      <span>All Categories</span>
                    </Link>
                  </SidebarMenuButton>
                </SidebarMenuItem>
              </SidebarMenu>
            </SidebarGroupContent>
          </SidebarGroup>
        )}

        {/* Chat & History - Show on AI page with scroll */}
        {isAIPage && (
          <SidebarGroup className="flex-1 overflow-hidden">
            <SidebarGroupLabel>Chat & History</SidebarGroupLabel>
            <SidebarGroupContent className="flex-1 overflow-hidden">
              <div className="h-full overflow-hidden">
                <ChatHistory />
              </div>
            </SidebarGroupContent>
          </SidebarGroup>
        )}

        {/* Other sections - Show when not on AI page */}
        {!isAIPage && (
          <>
            {/* Campaigns Section */}
            <SidebarGroup>
              <SidebarGroupLabel>Campaigns</SidebarGroupLabel>
              <SidebarGroupContent>
                <SidebarMenu>
                  {campaignRoutes.map((route) => (
                    <SidebarMenuItem key={route.href}>
                      <SidebarMenuButton asChild isActive={route.active}>
                        <Link href={route.href}>
                          <route.icon />
                          <span>{route.label}</span>
                        </Link>
                      </SidebarMenuButton>
                    </SidebarMenuItem>
                  ))}
                </SidebarMenu>
              </SidebarGroupContent>
            </SidebarGroup>

            {/* Management Section */}
            <SidebarGroup>
              <SidebarGroupLabel>Management</SidebarGroupLabel>
              <SidebarGroupContent>
                <SidebarMenu>
                  {managementRoutes.map((route) => (
                    <SidebarMenuItem key={route.href}>
                      <SidebarMenuButton asChild isActive={route.active}>
                        <Link href={route.href}>
                          <route.icon />
                          <span>{route.label}</span>
                        </Link>
                      </SidebarMenuButton>
                    </SidebarMenuItem>
                  ))}
                </SidebarMenu>
              </SidebarGroupContent>
            </SidebarGroup>

            {/* Quick Actions */}
            <SidebarGroup>
              <SidebarGroupLabel>Quick Actions</SidebarGroupLabel>
              <SidebarGroupContent>
                <SidebarMenu>
                  {quickActions.map((action) => (
                    <SidebarMenuItem key={action.href}>
                      <SidebarMenuButton asChild>
                        <Link href={action.href}>
                          <action.icon />
                          <span>{action.label}</span>
                        </Link>
                      </SidebarMenuButton>
                    </SidebarMenuItem>
                  ))}
                </SidebarMenu>
              </SidebarGroupContent>
            </SidebarGroup>
          </>
        )}
      </SidebarContent>

      <SidebarFooter className="border-sidebar-border border-t">
        <SidebarMenu>
          {user && (
            <SidebarMenuItem>
              <DropdownMenu>
                <DropdownMenuTrigger asChild>
                  <SidebarMenuButton
                    size="lg"
                    className="data-[state=open]:bg-sidebar-accent data-[state=open]:text-sidebar-accent-foreground"
                  >
                    <Avatar className="h-8 w-8 rounded-lg">
                      <AvatarImage 
                        src={user.image || ""} 
                        alt={user.name || "User"} 
                      />
                      <AvatarFallback className="rounded-lg">
                        {user.name?.charAt(0)?.toUpperCase() || "U"}
                      </AvatarFallback>
                    </Avatar>
                    <div className="grid flex-1 text-left text-sm leading-tight">
                      <span className="truncate font-semibold">{user.name}</span>
                      <span className="truncate text-xs">{user.email}</span>
                    </div>
                    <ChevronsUpDown className="ml-auto size-4" />
                  </SidebarMenuButton>
                </DropdownMenuTrigger>
                <DropdownMenuContent
                  className="w-[--radix-dropdown-menu-trigger-width] min-w-56 rounded-lg"
                  side="bottom"
                  align="end"
                  sideOffset={4}
                >
                  <DropdownMenuLabel className="p-0 font-normal">
                    <div className="flex items-center gap-2 px-1 py-1.5 text-left text-sm">
                      <Avatar className="h-8 w-8 rounded-lg">
                        <AvatarImage 
                          src={user.image || ""} 
                          alt={user.name || "User"} 
                        />
                        <AvatarFallback className="rounded-lg">
                          {user.name?.charAt(0)?.toUpperCase() || "U"}
                        </AvatarFallback>
                      </Avatar>
                      <div className="grid flex-1 text-left text-sm leading-tight">
                        <span className="truncate font-semibold">{user.name}</span>
                        <span className="truncate text-xs">{user.email}</span>
                      </div>
                    </div>
                  </DropdownMenuLabel>
                  <DropdownMenuSeparator />
                  <DropdownMenuItem asChild>
                    <Link href="/dashboard/profile">
                      <User />
                      Profile
                    </Link>
                  </DropdownMenuItem>
                  <DropdownMenuItem asChild>
                    <Link href="/dashboard/settings">
                      <Settings />
                      Settings
                    </Link>
                  </DropdownMenuItem>
                  <DropdownMenuSeparator />
                  <DropdownMenuItem onClick={onSignOut}>
                    <LogOut />
                    Log out
                  </DropdownMenuItem>
                </DropdownMenuContent>
              </DropdownMenu>
            </SidebarMenuItem>
          )}
        </SidebarMenu>
      </SidebarFooter>
    </Sidebar>
  )
}
