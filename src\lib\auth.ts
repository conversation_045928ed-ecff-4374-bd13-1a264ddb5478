import GoogleProvider from "next-auth/providers/google"
import { PrismaAdapter } from "@auth/prisma-adapter"
import { prisma } from "@/lib/prisma"
import { NextAuthOptions } from "next-auth"

export const authOptions: NextAuthOptions = {
  adapter: PrismaAdapter(prisma) as any,
  secret: process.env.NEXTAUTH_SECRET,
  providers: [
    GoogleProvider({
      clientId: process.env.GOOGLE_CLIENT_ID!,
      clientSecret: process.env.GOOGLE_CLIENT_SECRET!,
      authorization: {
        params: {
          scope: "openid email profile https://mail.google.com/",
          access_type: "offline", // This requests a refresh token
          prompt: "consent", // This forces the consent screen to show, ensuring we get a refresh token
        },
      },
    }),
  ],
  callbacks: {
    async signIn({ user, account, profile }: any) {
      return true
    },
    async session({ session, user }: any) {
      if (session.user && user) {
        session.user.id = user.id
        const userData = await prisma.user.findUnique({
          where: { id: user.id },
          select: { gmailConnected: true, gmailRefreshToken: true }
        })
        session.user.gmailConnected = userData?.gmailConnected || false
      }
      return session
    },
    async jwt({ token, user, account }: any) {
      if (account) {
        token.accessToken = account.access_token
        token.refreshToken = account.refresh_token
      }
      return token
    },
  },
  events: {
    async linkAccount({ user, account, profile }: any) {
      // Handle Gmail token storage after account is successfully linked
      if (account?.provider === "google") {
        console.log("🔗 Account linked for user:", user.email)
        
        // Check if this Google account has Gmail scope
        const hasGmailScope = account.scope?.includes("https://mail.google.com/")
        
        console.log("🔍 Scope check:", {
          fullScope: account.scope,
          hasGmailScope,
          hasRefreshToken: !!account.refresh_token,
          hasAccessToken: !!account.access_token
        })
        
        if (hasGmailScope) {
          try {
            // Update user with Gmail tokens after account linking
            const updateData: any = {
              gmailConnected: true,
              dailySendLimit: 250,
              dailySendCount: 0,
              lastSendReset: new Date()
            }
            
            // Only update refresh token if we have one
            if (account.refresh_token) {
              updateData.gmailRefreshToken = account.refresh_token
              console.log("💾 Storing refresh token for user:", user.email)
            }
            
            await prisma.user.update({
              where: { email: user.email! },
              data: updateData
            })
          } catch (error) {
            console.error("Error saving Gmail tokens:", error)
          }
        } else {
          console.log("Google account linked without Gmail scope for user:", user.email, "scope:", account.scope)
        }
      }
    },
  },
  pages: {
    signIn: "/auth/signin",
    error: "/auth/error",
  },
  session: {
    strategy: "database" as const,
  },
  debug: process.env.NODE_ENV === "development",
}
