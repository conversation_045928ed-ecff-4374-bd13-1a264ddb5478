// Background sync manager for draft synchronization between cache and Gmail API

import { draftCache, CachedDraft } from './draftCache'
import { DraftData } from '../gmail/types'

export interface SyncResult {
  success: boolean
  draftId?: string
  gmailDraftId?: string
  error?: string
}

export interface SyncStats {
  totalProcessed: number
  successful: number
  failed: number
  errors: string[]
}

class DraftSyncManager {
  private syncInProgress = false
  private syncQueue: Set<string> = new Set()
  private retryAttempts = new Map<string, number>()
  private maxRetries = 3
  private syncInterval: NodeJS.Timeout | null = null
  private isOnline = true

  constructor() {
    // Monitor online/offline status
    if (typeof window !== 'undefined') {
      this.isOnline = navigator.onLine
      window.addEventListener('online', () => {
        this.isOnline = true
        this.startPeriodicSync()
      })
      window.addEventListener('offline', () => {
        this.isOnline = false
        this.stopPeriodicSync()
      })
    }
  }

  startPeriodicSync(intervalMs = 30000): void {
    if (this.syncInterval) {
      clearInterval(this.syncInterval)
    }

    this.syncInterval = setInterval(() => {
      if (this.isOnline && !this.syncInProgress) {
        this.syncPendingDrafts()
      }
    }, intervalMs)
  }

  stopPeriodicSync(): void {
    if (this.syncInterval) {
      clearInterval(this.syncInterval)
      this.syncInterval = null
    }
  }

  async queueDraftForSync(draftId: string): Promise<void> {
    this.syncQueue.add(draftId)
    
    // Trigger immediate sync if online and not already syncing
    if (this.isOnline && !this.syncInProgress) {
      setTimeout(() => this.syncPendingDrafts(), 100)
    }
  }

  async syncPendingDrafts(userId?: string): Promise<SyncStats> {
    if (this.syncInProgress || !this.isOnline) {
      return { totalProcessed: 0, successful: 0, failed: 0, errors: [] }
    }

    this.syncInProgress = true
    const stats: SyncStats = { totalProcessed: 0, successful: 0, failed: 0, errors: [] }

    try {
      // Get all pending drafts for the user
      const pendingDrafts = userId 
        ? await draftCache.getPendingSyncDrafts(userId)
        : await this.getAllPendingSyncDrafts()

      stats.totalProcessed = pendingDrafts.length

      for (const draft of pendingDrafts) {
        try {
          const result = await this.syncSingleDraft(draft)
          if (result.success) {
            stats.successful++
            this.syncQueue.delete(draft.id)
            this.retryAttempts.delete(draft.id)
          } else {
            stats.failed++
            stats.errors.push(`Draft ${draft.id}: ${result.error}`)
            await this.handleSyncError(draft, result.error || 'Unknown error')
          }
        } catch (error) {
          stats.failed++
          const errorMsg = error instanceof Error ? error.message : 'Unknown error'
          stats.errors.push(`Draft ${draft.id}: ${errorMsg}`)
          await this.handleSyncError(draft, errorMsg)
        }
      }
    } catch (error) {
      console.error('Error during draft sync:', error)
      stats.errors.push(`Sync process error: ${error instanceof Error ? error.message : 'Unknown error'}`)
    } finally {
      this.syncInProgress = false
    }

    return stats
  }

  private async getAllPendingSyncDrafts(): Promise<CachedDraft[]> {
    // This would need to be implemented to get all users' pending drafts
    // For now, we'll assume we have the current user's ID
    const userId = await this.getCurrentUserId()
    return userId ? await draftCache.getPendingSyncDrafts(userId) : []
  }

  private async getCurrentUserId(): Promise<string | null> {
    // This should get the current user ID from your auth system
    // Implementation depends on your auth setup
    try {
      const response = await fetch('/api/auth/user')
      if (response.ok) {
        const user = await response.json()
        return user.id
      }
    } catch (error) {
      console.error('Error getting current user:', error)
    }
    return null
  }

  private async syncSingleDraft(draft: CachedDraft): Promise<SyncResult> {
    try {
      if (draft.isDeleted) {
        return await this.syncDeletedDraft(draft)
      } else if (draft.gmailDraftId) {
        return await this.syncUpdatedDraft(draft)
      } else {
        return await this.syncNewDraft(draft)
      }
    } catch (error) {
      return {
        success: false,
        draftId: draft.id,
        error: error instanceof Error ? error.message : 'Unknown error'
      }
    }
  }

  private async syncNewDraft(draft: CachedDraft): Promise<SyncResult> {
    const draftData: DraftData = {
      to: draft.to,
      cc: draft.cc,
      bcc: draft.bcc,
      subject: draft.subject,
      htmlBody: draft.htmlBody,
      textBody: draft.textBody,
      threadId: draft.threadId,
      replyToMessageId: draft.replyToMessageId,
      messageId: draft.messageId,
      references: draft.references,
      inReplyTo: draft.inReplyTo,
      attachments: draft.attachments
    }

    const response = await fetch('/api/gmail/drafts/create', {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify(draftData)
    })

    if (!response.ok) {
      throw new Error(`Failed to create draft: ${response.statusText}`)
    }

    const result = await response.json()
    if (result.success && result.draftId) {
      await draftCache.markDraftSynced(draft.id, result.draftId)
      return {
        success: true,
        draftId: draft.id,
        gmailDraftId: result.draftId
      }
    } else {
      throw new Error(result.error || 'Failed to create draft')
    }
  }

  private async syncUpdatedDraft(draft: CachedDraft): Promise<SyncResult> {
    const draftData: DraftData = {
      to: draft.to,
      cc: draft.cc,
      bcc: draft.bcc,
      subject: draft.subject,
      htmlBody: draft.htmlBody,
      textBody: draft.textBody,
      threadId: draft.threadId,
      replyToMessageId: draft.replyToMessageId,
      messageId: draft.messageId,
      references: draft.references,
      inReplyTo: draft.inReplyTo,
      attachments: draft.attachments
    }

    const response = await fetch('/api/gmail/drafts/update', {
      method: 'PUT',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({
        draftId: draft.gmailDraftId,
        ...draftData
      })
    })

    if (!response.ok) {
      throw new Error(`Failed to update draft: ${response.statusText}`)
    }

    const result = await response.json()
    if (result.success) {
      await draftCache.markDraftSynced(draft.id, draft.gmailDraftId!)
      return {
        success: true,
        draftId: draft.id,
        gmailDraftId: draft.gmailDraftId
      }
    } else {
      throw new Error(result.error || 'Failed to update draft')
    }
  }

  private async syncDeletedDraft(draft: CachedDraft): Promise<SyncResult> {
    if (!draft.gmailDraftId) {
      // Draft was never synced to Gmail, just remove from cache
      await draftCache.purgeDraft(draft.id)
      return {
        success: true,
        draftId: draft.id
      }
    }

    const response = await fetch('/api/gmail/drafts/delete', {
      method: 'DELETE',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({ draftId: draft.gmailDraftId })
    })

    if (!response.ok) {
      throw new Error(`Failed to delete draft: ${response.statusText}`)
    }

    const result = await response.json()
    if (result.success) {
      await draftCache.purgeDraft(draft.id)
      return {
        success: true,
        draftId: draft.id,
        gmailDraftId: draft.gmailDraftId
      }
    } else {
      throw new Error(result.error || 'Failed to delete draft')
    }
  }

  private async handleSyncError(draft: CachedDraft, error: string): Promise<void> {
    const attempts = this.retryAttempts.get(draft.id) || 0
    
    if (attempts < this.maxRetries) {
      this.retryAttempts.set(draft.id, attempts + 1)
      this.syncQueue.add(draft.id)
    } else {
      // Max retries reached, mark as error
      await draftCache.markDraftSyncError(draft.id, error)
      this.retryAttempts.delete(draft.id)
      this.syncQueue.delete(draft.id)
    }
  }

  async forceSyncDraft(draftId: string): Promise<SyncResult> {
    const draft = await draftCache.getDraft(draftId)
    if (!draft) {
      return {
        success: false,
        draftId,
        error: 'Draft not found in cache'
      }
    }

    return await this.syncSingleDraft(draft)
  }

  getSyncStatus(): {
    isOnline: boolean
    syncInProgress: boolean
    queueSize: number
  } {
    return {
      isOnline: this.isOnline,
      syncInProgress: this.syncInProgress,
      queueSize: this.syncQueue.size
    }
  }

  async retrySyncErrors(userId: string): Promise<SyncStats> {
    // Get all drafts with sync errors and retry them
    const drafts = await draftCache.getDraftsByUser(userId)
    const errorDrafts = drafts.filter(d => d.syncStatus === 'error')
    
    // Reset retry attempts for error drafts
    errorDrafts.forEach(draft => {
      this.retryAttempts.delete(draft.id)
      this.syncQueue.add(draft.id)
      // Reset sync status to pending
      draftCache.updateDraft(draft.id, { syncStatus: 'pending', syncError: undefined })
    })

    return await this.syncPendingDrafts(userId)
  }
}

// Singleton instance
export const draftSyncManager = new DraftSyncManager()

// Auto-start periodic sync
if (typeof window !== 'undefined') {
  draftSyncManager.startPeriodicSync()
}
