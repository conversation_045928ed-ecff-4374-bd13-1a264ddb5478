'use client'

import { useState, useEffect } from 'react'
import { useSession } from 'next-auth/react'
import { useRouter, useSearchParams } from 'next/navigation'
import { CalendarView } from '@/components/calendar/calendar-view'
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Button } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { Alert, AlertDescription } from "@/components/ui/alert"
import { 
  Calendar as CalendarIcon,
  Sparkles,
  Bot,
  Clock,
  AlertCircle,
  CheckCircle,
  TrendingUp,
  Shield
} from "lucide-react"
import { EncryptedCalendarEvent } from "@/lib/calendar-encryption"
import { ActionItem } from "@/lib/gemini"

interface CalendarStats {
  totalEvents: number
  upcomingEvents: number
  completedEvents: number
  highPriorityEvents: number
  aiGeneratedEvents: number
}

export default function CalendarPage() {
  const { data: session, status } = useSession()
  const router = useRouter()
  const searchParams = useSearchParams()
  
  const [stats, setStats] = useState<CalendarStats>({
    totalEvents: 0,
    upcomingEvents: 0,
    completedEvents: 0,
    highPriorityEvents: 0,
    aiGeneratedEvents: 0
  })
  
  const [recentAIEvents, setRecentAIEvents] = useState<EncryptedCalendarEvent[]>([])
  const [isCreatingFromAI, setIsCreatingFromAI] = useState(false)

  // Check for AI-generated events from URL params (from AI analysis page)
  const aiEventData = searchParams?.get('aiEvent')

  useEffect(() => {
    if (status === 'unauthenticated') {
      router.push('/auth/signin')
    }
  }, [status, router])

  useEffect(() => {
    if (session?.user?.email) {
      loadCalendarStats()
      loadRecentAIEvents()
    }
  }, [session])

  useEffect(() => {
    // Handle AI-generated event from URL params
    if (aiEventData && !isCreatingFromAI) {
      try {
        const eventData = JSON.parse(decodeURIComponent(aiEventData))
        createAIEvent(eventData)
      } catch (error) {
        console.error('Error parsing AI event data:', error)
      }
    }
  }, [aiEventData, isCreatingFromAI])

  const loadCalendarStats = async () => {
    try {
      const response = await fetch('/api/calendar')
      const data = await response.json()
      
      if (data.events) {
        const events = data.events
        const now = new Date()
        
        const stats: CalendarStats = {
          totalEvents: events.length,
          upcomingEvents: events.filter((e: EncryptedCalendarEvent) => 
            new Date(e.startDate) > now && e.status === 'pending'
          ).length,
          completedEvents: events.filter((e: EncryptedCalendarEvent) => 
            e.status === 'confirmed'
          ).length,
          highPriorityEvents: events.filter((e: EncryptedCalendarEvent) => 
            e.priority === 'high' && e.status === 'pending'
          ).length,
          aiGeneratedEvents: events.filter((e: EncryptedCalendarEvent) => 
            e.sourceType === 'ai-extracted'
          ).length
        }
        
        setStats(stats)
      }
    } catch (error) {
      console.error('Error loading calendar stats:', error)
    }
  }

  const loadRecentAIEvents = async () => {
    try {
      const response = await fetch('/api/calendar?category=all&sourceType=ai_generated')
      const data = await response.json()
      
      if (data.events) {
        // Get the 5 most recent AI-generated events
        const aiEvents = data.events
          .filter((e: EncryptedCalendarEvent) => e.sourceType === 'ai-extracted')
          .sort((a: EncryptedCalendarEvent, b: EncryptedCalendarEvent) => 
            new Date(b.startDate).getTime() - new Date(a.startDate).getTime()
          )
          .slice(0, 5)
        
        setRecentAIEvents(aiEvents)
      }
    } catch (error) {
      console.error('Error loading recent AI events:', error)
    }
  }

  const createAIEvent = async (actionItem: ActionItem) => {
    setIsCreatingFromAI(true)
    
    try {
      const eventData: Partial<EncryptedCalendarEvent> = {
        title: actionItem.title,
        description: actionItem.description,
        startDate: actionItem.dueDate || new Date(),
        location: actionItem.location,
        attendees: actionItem.attendees || [],
        priority: actionItem.urgency === 'high' ? 'high' : actionItem.urgency === 'low' ? 'low' : 'medium',
        category: actionItem.type === 'meeting' ? 'meeting' : actionItem.type === 'task' ? 'task' : actionItem.type,
        status: 'pending',
        sourceType: 'ai-extracted'
      }

      const response = await fetch('/api/calendar', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(eventData)
      })

      const data = await response.json()
      
      if (data.event) {
        // Refresh stats and recent events
        await loadCalendarStats()
        await loadRecentAIEvents()
        
        // Clear URL params
        const url = new URL(window.location.href)
        url.searchParams.delete('aiEvent')
        router.replace(url.pathname)
        
        // Show success message
        alert(`Successfully created event: ${data.event.title}`)
      }
    } catch (error) {
      console.error('Error creating AI event:', error)
      alert('Failed to create event from AI analysis')
    } finally {
      setIsCreatingFromAI(false)
    }
  }

  const analyzeEmailsForEvents = async () => {
    try {
      router.push('/dashboard/ai?tab=analysis&prompt=extract-calendar-events')
    } catch (error) {
      console.error('Error navigating to AI analysis:', error)
    }
  }

  if (status === 'loading') {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-[#1a73e8]"></div>
      </div>
    )
  }

  return (
    <div className="space-y-6 p-6 bg-white min-h-screen">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold text-[#202124] flex items-center gap-3">
            <div className="p-2 bg-gradient-to-r from-[#1a73e8] to-[#4285f4] rounded-lg">
              <CalendarIcon className="h-8 w-8 text-white" />
            </div>
            Smart Calendar
          </h1>
          <p className="text-[#5f6368] mt-2">
            AI-powered calendar with encrypted event storage and email integration
          </p>
        </div>
        
        <div className="flex items-center gap-3">
          <Badge variant="outline" className="bg-[#e8f0fe] text-[#1a73e8] border-[#1a73e8]">
            <Shield className="h-3 w-3 mr-1" />
            Encrypted
          </Badge>
          <Button 
            onClick={analyzeEmailsForEvents}
            className="bg-[#1a73e8] hover:bg-[#1557b0]"
          >
            <Sparkles className="h-4 w-4 mr-2" />
            Analyze Emails for Events
          </Button>
        </div>
      </div>

      {/* AI Event Creation Alert */}
      {isCreatingFromAI && (
        <Alert className="border-[#1a73e8] bg-[#e8f0fe]">
          <Bot className="h-4 w-4" />
          <AlertDescription>
            Creating calendar event from AI analysis...
          </AlertDescription>
        </Alert>
      )}

      {/* Statistics Cards */}
      <div className="grid grid-cols-1 md:grid-cols-5 gap-4">
        <Card className="border-[#e8eaed]">
          <CardHeader className="pb-2">
            <CardTitle className="text-sm font-medium text-[#5f6368] flex items-center gap-2">
              <CalendarIcon className="h-4 w-4" />
              Total Events
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-[#202124]">{stats.totalEvents}</div>
            <p className="text-xs text-[#5f6368] mt-1">All events</p>
          </CardContent>
        </Card>

        <Card className="border-[#e8eaed]">
          <CardHeader className="pb-2">
            <CardTitle className="text-sm font-medium text-[#5f6368] flex items-center gap-2">
              <Clock className="h-4 w-4" />
              Upcoming
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-[#1a73e8]">{stats.upcomingEvents}</div>
            <p className="text-xs text-[#5f6368] mt-1">Pending events</p>
          </CardContent>
        </Card>

        <Card className="border-[#e8eaed]">
          <CardHeader className="pb-2">
            <CardTitle className="text-sm font-medium text-[#5f6368] flex items-center gap-2">
              <CheckCircle className="h-4 w-4" />
              Completed
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-[#34a853]">{stats.completedEvents}</div>
            <p className="text-xs text-[#5f6368] mt-1">Finished events</p>
          </CardContent>
        </Card>

        <Card className="border-[#e8eaed]">
          <CardHeader className="pb-2">
            <CardTitle className="text-sm font-medium text-[#5f6368] flex items-center gap-2">
              <AlertCircle className="h-4 w-4" />
              High Priority
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-[#ea4335]">{stats.highPriorityEvents}</div>
            <p className="text-xs text-[#5f6368] mt-1">Important events</p>
          </CardContent>
        </Card>

        <Card className="border-[#e8eaed]">
          <CardHeader className="pb-2">
            <CardTitle className="text-sm font-medium text-[#5f6368] flex items-center gap-2">
              <Bot className="h-4 w-4" />
              AI Generated
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-[#9c27b0]">{stats.aiGeneratedEvents}</div>
            <p className="text-xs text-[#5f6368] mt-1">From email analysis</p>
          </CardContent>
        </Card>
      </div>

      {/* Recent AI-Generated Events */}
      {recentAIEvents.length > 0 && (
        <Card className="border-[#e8eaed]">
          <CardHeader>
            <CardTitle className="text-lg text-[#202124] flex items-center gap-2">
              <TrendingUp className="h-5 w-5" />
              Recent AI-Generated Events
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-3">
              {recentAIEvents.map((event) => (
                <div key={event.id} className="flex items-center justify-between p-3 bg-[#f8f9fa] rounded-lg border border-[#e8eaed]">
                  <div className="flex-1">
                    <div className="flex items-center gap-2 mb-1">
                      <h4 className="font-medium text-[#202124]">{event.title}</h4>
                      <Badge className={`text-xs ${
                        event.priority === 'high' 
                          ? 'bg-red-100 text-red-800' 
                          : event.priority === 'medium'
                          ? 'bg-orange-100 text-orange-800'
                          : 'bg-green-100 text-green-800'
                      }`}>
                        {event.priority?.toUpperCase()}
                      </Badge>
                    </div>
                    {event.description && (
                      <p className="text-sm text-[#5f6368] mb-1">{event.description}</p>
                    )}
                    <div className="flex items-center gap-4 text-xs text-[#5f6368]">
                      <span>{new Date(event.startDate).toLocaleDateString()}</span>
                      <span>{event.category}</span>
                      {event.location && <span>📍 {event.location}</span>}
                    </div>
                  </div>
                  <div className="flex items-center gap-2">
                    <Badge variant="outline" className="bg-[#e8f0fe] text-[#1a73e8]">
                      <Bot className="h-3 w-3 mr-1" />
                      AI
                    </Badge>
                    <Badge variant={event.status === 'confirmed' ? 'default' : 'secondary'}>
                      {event.status}
                    </Badge>
                  </div>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
      )}

      {/* Main Calendar View */}
      <CalendarView />
    </div>
  )
} 