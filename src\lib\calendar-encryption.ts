import crypto from 'crypto'
import { modernEncryption, type EncryptionContext } from './encryption'

export interface EncryptedCalendarEvent {
  id?: string
  userId: string
  title: string
  description?: string
  startDate: Date
  endDate?: Date
  location?: string
  category?: string
  priority?: 'low' | 'medium' | 'high'
  status?: 'pending' | 'confirmed' | 'cancelled'
  sourceType?: 'ai-extracted' | 'manual' | 'gmail-auto'
  sourceId?: string
  isRecurring: boolean
  recurrencePattern?: string
  attendees?: string[]
  reminderMinutes?: number
  createdAt?: Date
  updatedAt?: Date
}

export interface DatabaseCalendarEvent {
  id: string
  userId: string
  title: string // Encrypted
  description?: string // Encrypted
  startDate: Date
  endDate?: Date
  location?: string // Encrypted
  category?: string
  priority?: string
  status?: string
  sourceType?: string
  sourceId?: string
  isRecurring: boolean
  recurrencePattern?: string // Encrypted
  attendees?: string // Encrypted JSON
  reminderMinutes?: number
  encryptionSalt: string
  createdAt: Date
  updatedAt: Date
}

/**
 * Production calendar encryption service using AES-256-GCM
 * Clean, fast, and secure - no legacy support
 */
export class CalendarEncryptionService {
  private readonly context: EncryptionContext = 'calendar'

  /**
   * Encrypt a calendar event for secure database storage
   */
  async encryptCalendarEvent(calendarEvent: EncryptedCalendarEvent): Promise<DatabaseCalendarEvent> {
    try {
      // Generate event-specific entropy that will be stored for decryption
      const eventId = calendarEvent.id || crypto.randomUUID()
      const eventEntropy = modernEncryption.generateEncryptionSalt(
        calendarEvent.userId,
        eventId,
        this.context
      )
      
      // Encrypt sensitive fields using the stored entropy
      const encryptedTitle = await modernEncryption.encryptData(
        calendarEvent.title,
        calendarEvent.userId,
        this.context,
        `title:${eventEntropy}`
      )
      
      const encryptedDescription = calendarEvent.description ? 
        await modernEncryption.encryptData(
          calendarEvent.description,
          calendarEvent.userId,
          this.context,
          `description:${eventEntropy}`
        ) : undefined
      
      const encryptedLocation = calendarEvent.location ?
        await modernEncryption.encryptData(
          calendarEvent.location,
          calendarEvent.userId,
          this.context,
          `location:${eventEntropy}`
        ) : undefined
      
      const encryptedRecurrencePattern = calendarEvent.recurrencePattern ?
        await modernEncryption.encryptData(
          calendarEvent.recurrencePattern,
          calendarEvent.userId,
          this.context,
          `recurrence:${eventEntropy}`
        ) : undefined
      
      const encryptedAttendees = calendarEvent.attendees ?
        await modernEncryption.encryptData(
          JSON.stringify(calendarEvent.attendees),
          calendarEvent.userId,
          this.context,
          `attendees:${eventEntropy}`
        ) : undefined
      
      return {
        id: eventId,
        userId: calendarEvent.userId,
        title: encryptedTitle,
        description: encryptedDescription,
        startDate: calendarEvent.startDate,
        endDate: calendarEvent.endDate,
        location: encryptedLocation,
        category: calendarEvent.category,
        priority: calendarEvent.priority,
        status: calendarEvent.status,
        sourceType: calendarEvent.sourceType,
        sourceId: calendarEvent.sourceId,
        isRecurring: calendarEvent.isRecurring,
        recurrencePattern: encryptedRecurrencePattern,
        attendees: encryptedAttendees,
        reminderMinutes: calendarEvent.reminderMinutes,
        encryptionSalt: eventEntropy,
        createdAt: calendarEvent.createdAt || new Date(),
        updatedAt: calendarEvent.updatedAt || new Date()
      }
    } catch (error) {
      throw new Error(`Failed to encrypt calendar event: ${error instanceof Error ? error.message : 'Unknown error'}`)
    }
  }

  /**
   * Decrypt a calendar event from database
   */
  async decryptCalendarEvent(dbEvent: DatabaseCalendarEvent): Promise<EncryptedCalendarEvent | null> {
    try {
      // Use the stored entropy for decryption - this is critical!
      const eventEntropy = dbEvent.encryptionSalt
      
      // Decrypt title
      const title = await modernEncryption.decryptData(
        dbEvent.title,
        dbEvent.userId,
        this.context,
        `title:${eventEntropy}`
      )
      
      // Decrypt optional fields
      const description = dbEvent.description ?
        await modernEncryption.decryptData(
          dbEvent.description,
          dbEvent.userId,
          this.context,
          `description:${eventEntropy}`
        ) : undefined
      
      const location = dbEvent.location ?
        await modernEncryption.decryptData(
          dbEvent.location,
          dbEvent.userId,
          this.context,
          `location:${eventEntropy}`
        ) : undefined
      
      const recurrencePattern = dbEvent.recurrencePattern ?
        await modernEncryption.decryptData(
          dbEvent.recurrencePattern,
          dbEvent.userId,
          this.context,
          `recurrence:${eventEntropy}`
        ) : undefined
      
      let attendees: string[] | undefined = undefined
      if (dbEvent.attendees) {
        try {
          const attendeesString = await modernEncryption.decryptData(
            dbEvent.attendees,
            dbEvent.userId,
            this.context,
            `attendees:${eventEntropy}`
          )
          attendees = JSON.parse(attendeesString)
        } catch (error) {
          console.warn(`Failed to decrypt attendees for event ${dbEvent.id}:`, error)
          // Continue without attendees rather than failing the whole event
        }
      }
      
      return {
        id: dbEvent.id,
        userId: dbEvent.userId,
        title,
        description,
        startDate: dbEvent.startDate,
        endDate: dbEvent.endDate,
        location,
        category: dbEvent.category,
        priority: dbEvent.priority as 'low' | 'medium' | 'high',
        status: dbEvent.status as 'pending' | 'confirmed' | 'cancelled',
        sourceType: dbEvent.sourceType as 'ai-extracted' | 'manual' | 'gmail-auto',
        sourceId: dbEvent.sourceId,
        isRecurring: dbEvent.isRecurring ?? false,
        recurrencePattern,
        attendees,
        reminderMinutes: dbEvent.reminderMinutes,
        createdAt: dbEvent.createdAt,
        updatedAt: dbEvent.updatedAt
      }
    } catch (error) {
      console.error(`Failed to decrypt calendar event ${dbEvent.id}:`, error)
      // Return null instead of throwing to allow graceful handling
      return null
    }
  }

  /**
   * Batch encrypt multiple calendar events
   */
  async encryptCalendarEvents(events: EncryptedCalendarEvent[]): Promise<DatabaseCalendarEvent[]> {
    return Promise.all(
      events.map(event => this.encryptCalendarEvent(event))
    )
  }

  /**
   * Batch decrypt multiple calendar events with error handling
   */
  async decryptCalendarEvents(dbEvents: DatabaseCalendarEvent[]): Promise<EncryptedCalendarEvent[]> {
    const results = await Promise.allSettled(
      dbEvents.map(event => this.decryptCalendarEvent(event))
    )
    
    return results
      .filter((result): result is PromiseFulfilledResult<EncryptedCalendarEvent | null> => 
        result.status === 'fulfilled' && result.value !== null
      )
      .map(result => result.value as EncryptedCalendarEvent)
  }

  /**
   * Check if calendar data is encrypted with modern format
   */
  isModernEncrypted(data: string): boolean {
    return modernEncryption.isEncrypted(data)
  }

  /**
   * Generate a new event ID
   */
  generateEventId(): string {
    return crypto.randomUUID()
  }

  /**
   * Update an existing encrypted event with new data
   */
  async updateEncryptedEvent(
    existingDbEvent: DatabaseCalendarEvent,
    updates: Partial<EncryptedCalendarEvent>
  ): Promise<DatabaseCalendarEvent> {
    try {
      // First decrypt the existing event
      const decryptedEvent = await this.decryptCalendarEvent(existingDbEvent)
      
      if (!decryptedEvent) {
        throw new Error('Failed to decrypt existing event')
      }
      
      // Apply updates
      const updatedEvent = {
        ...decryptedEvent,
        ...updates,
        id: existingDbEvent.id, // Keep the original ID
        userId: existingDbEvent.userId, // Keep the original user ID
        updatedAt: new Date()
      }
      
      // Re-encrypt with new data
      return await this.encryptCalendarEvent(updatedEvent)
    } catch (error) {
      throw new Error(`Failed to update calendar event: ${error instanceof Error ? error.message : 'Unknown error'}`)
    }
  }
}

// Export singleton instance
export const calendarEncryption = new CalendarEncryptionService()

export default calendarEncryption 