import { useState, useEffect, useCallback, useRef } from 'react'
import { draftCache, CachedDraft } from '@/lib/cache/draftCache'
import { draftSyncManager } from '@/lib/cache/draftSyncManager'
import { useOfflineDetection } from './useOfflineDetection'

export interface DraftData {
  to: string
  cc?: string
  bcc?: string
  subject: string
  htmlBody: string
  textBody?: string
  threadId?: string
  replyToMessageId?: string
  messageId?: string
  references?: string
  inReplyTo?: string
  attachments?: Array<{
    filename: string
    content: string
    mimeType: string
  }>
}

export interface DraftAutoSaveOptions {
  debounceMs?: number
  enabled?: boolean
  onSaveSuccess?: (draftId: string) => void
  onSaveError?: (error: string) => void
  onDraftRecovered?: (draftId: string) => void
}

export interface DraftAutoSaveReturn {
  draftId: string | null
  isSaving: boolean
  lastSaved: Date | null
  hasUnsavedChanges: boolean
  saveDraft: () => Promise<void>
  deleteDraft: () => Promise<void>
  sendDraft: () => Promise<void>
  error: string | null
}

export function useDraftAutoSave(
  draftData: DraftData,
  options: DraftAutoSaveOptions = {}
): DraftAutoSaveReturn {
  const {
    debounceMs = 3000, // Auto-save every 3 seconds
    enabled = true,
    onSaveSuccess,
    onSaveError,
    onDraftRecovered
  } = options

  const [draftId, setDraftId] = useState<string | null>(null)
  const [cachedDraft, setCachedDraft] = useState<CachedDraft | null>(null)
  const [isSaving, setIsSaving] = useState(false)
  const [lastSaved, setLastSaved] = useState<Date | null>(null)
  const [hasUnsavedChanges, setHasUnsavedChanges] = useState(false)
  const [error, setError] = useState<string | null>(null)

  const debounceTimerRef = useRef<NodeJS.Timeout | null>(null)
  const lastDraftDataRef = useRef<string>('')
  const isInitializedRef = useRef(false)
  const userIdRef = useRef<string | null>(null)

  // Use offline detection for network-aware operations
  const { networkStatus, addToOfflineQueue } = useOfflineDetection()

  // Check if draft data has meaningful content
  const hasContent = useCallback(() => {
    return draftData.to.trim() !== '' || 
           draftData.subject.trim() !== '' || 
           draftData.htmlBody.trim() !== ''
  }, [draftData])

  // Get current user ID
  const getCurrentUserId = useCallback(async (): Promise<string | null> => {
    if (userIdRef.current) return userIdRef.current

    try {
      const response = await fetch('/api/auth/user')
      if (response.ok) {
        const user = await response.json()
        userIdRef.current = user.id
        return user.id
      }
    } catch (error) {
      console.error('Error getting current user:', error)
    }
    return null
  }, [])

  // Save draft function - Cache first, then sync
  const saveDraft = useCallback(async () => {
    if (!hasContent() || isSaving) return

    setIsSaving(true)
    setError(null)

    try {
      const userId = await getCurrentUserId()
      if (!userId) {
        throw new Error('User not authenticated')
      }

      // Save to cache first (immediate, always works)
      let savedDraft: CachedDraft

      if (cachedDraft) {
        // Update existing cached draft
        savedDraft = await draftCache.updateDraft(cachedDraft.id, {
          ...draftData,
          userId,
          syncStatus: 'pending'
        }) as CachedDraft
      } else {
        // Create new cached draft
        savedDraft = await draftCache.saveDraft({
          ...draftData,
          userId,
          syncStatus: 'pending'
        })
      }

      setCachedDraft(savedDraft)
      setDraftId(savedDraft.id)
      setLastSaved(savedDraft.updatedAt)
      setHasUnsavedChanges(false)

      // Queue for background sync to Gmail API
      if (networkStatus.isOnline) {
        // Try immediate sync if online
        draftSyncManager.queueDraftForSync(savedDraft.id)
      } else {
        // Add to offline queue if offline
        addToOfflineQueue({
          type: savedDraft.gmailDraftId ? 'draft_update' : 'draft_create',
          data: { draftId: savedDraft.id, ...draftData },
          maxRetries: 3
        })
      }

      onSaveSuccess?.(savedDraft.id)
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Unknown error occurred'
      setError(errorMessage)
      onSaveError?.(errorMessage)
    } finally {
      setIsSaving(false)
    }
  }, [draftData, cachedDraft, hasContent, isSaving, networkStatus.isOnline, addToOfflineQueue, getCurrentUserId, onSaveSuccess, onSaveError])

  // Delete draft function - Cache first, then sync
  const deleteDraft = useCallback(async () => {
    if (!draftId || !cachedDraft) return

    try {
      // Mark as deleted in cache first
      await draftCache.deleteDraft(draftId)

      // Queue for background sync if has Gmail draft ID
      if (cachedDraft.gmailDraftId) {
        if (networkStatus.isOnline) {
          draftSyncManager.queueDraftForSync(draftId)
        } else {
          addToOfflineQueue({
            type: 'draft_delete',
            data: { draftId: cachedDraft.gmailDraftId },
            maxRetries: 3
          })
        }
      }

      // Clear local state
      setDraftId(null)
      setCachedDraft(null)
      setLastSaved(null)
      setHasUnsavedChanges(false)
      setError(null)
    } catch (err) {
      console.error('Error deleting draft:', err)
    }
  }, [draftId, cachedDraft, networkStatus.isOnline, addToOfflineQueue])

  // Send draft function - Cache first, then sync
  const sendDraft = useCallback(async () => {
    if (!draftId || !cachedDraft) return

    try {
      // If draft is not synced to Gmail yet, sync it first
      if (!cachedDraft.gmailDraftId) {
        await saveDraft() // This will sync to Gmail
        // Wait a moment for sync to complete
        await new Promise(resolve => setTimeout(resolve, 1000))

        // Get updated cached draft
        const updatedDraft = await draftCache.getDraft(draftId)
        if (!updatedDraft?.gmailDraftId) {
          throw new Error('Draft not synced to Gmail yet')
        }
      }

      // Send the draft
      if (networkStatus.isOnline) {
        const response = await fetch('/api/gmail/drafts/send', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json'
          },
          body: JSON.stringify({ draftId: cachedDraft.gmailDraftId })
        })

        if (!response.ok) {
          throw new Error('Failed to send draft')
        }

        // Remove from cache after successful send
        await draftCache.purgeDraft(draftId)
      } else {
        // Queue for sending when online
        addToOfflineQueue({
          type: 'draft_send',
          data: { draftId: cachedDraft.gmailDraftId },
          maxRetries: 3
        })
      }

      // Clear local state
      setDraftId(null)
      setCachedDraft(null)
      setLastSaved(null)
      setHasUnsavedChanges(false)
      setError(null)
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to send draft'
      setError(errorMessage)
      throw err
    }
  }, [draftId, cachedDraft, networkStatus.isOnline, addToOfflineQueue, saveDraft])

  // Auto-save effect with debouncing
  useEffect(() => {
    if (!enabled || !isInitializedRef.current) {
      isInitializedRef.current = true
      return
    }

    const currentDraftData = JSON.stringify(draftData)
    
    // Check if data has changed
    if (currentDraftData !== lastDraftDataRef.current) {
      setHasUnsavedChanges(true)
      lastDraftDataRef.current = currentDraftData

      // Clear existing timer
      if (debounceTimerRef.current) {
        clearTimeout(debounceTimerRef.current)
      }

      // Set new timer for auto-save
      if (hasContent()) {
        debounceTimerRef.current = setTimeout(() => {
          saveDraft()
        }, debounceMs)
      }
    }

    // Cleanup timer on unmount
    return () => {
      if (debounceTimerRef.current) {
        clearTimeout(debounceTimerRef.current)
      }
    }
  }, [draftData, enabled, debounceMs, hasContent, saveDraft])

  // Cleanup on unmount
  useEffect(() => {
    return () => {
      if (debounceTimerRef.current) {
        clearTimeout(debounceTimerRef.current)
      }
    }
  }, [])

  return {
    draftId,
    isSaving,
    lastSaved,
    hasUnsavedChanges,
    saveDraft,
    deleteDraft,
    sendDraft,
    error
  }
}
